import React from 'react';
import { contactInfo } from '@/data/mockData';
import Image from '@/components/ui/Image';
import Container from '@/components/ui/Container';

const HeroSection: React.FC = () => {
  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/hero-bg.jpg"
          alt="Beautiful flower arrangement"
          fill
          priority
          className="object-cover"
          unsplashQuery="flowers,bouquet,florist"
          unsplashSize="1920x1080"
        />
        <div className="absolute inset-0 bg-black bg-opacity-30" />
      </div>

      {/* Contact Info Cards */}
      <div className="absolute top-20 left-8 right-8 z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-6xl mx-auto">
          {[...Array(4)].map((_, index) => (
            <div
              key={index}
              className="bg-white bg-opacity-90 backdrop-blur-sm p-4 rounded-lg shadow-lg text-center"
            >
              <div className="text-sm font-medium text-gray-800 mb-1">
                {contactInfo.address}
              </div>
              <div className="text-xs text-gray-600">
                {contactInfo.phone}
              </div>
              <div className="text-xs text-gray-600 mt-1">
                {contactInfo.hours}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <Container className="relative z-10 text-center text-white">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
          We Produce Beauty
          <br />
          <span className="text-green-300">Inspired by Flora</span>
        </h1>
        <p className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto leading-relaxed">
          Lorem ipsum dolor sit amet, pri autem nemore bonorum te. Autem fierent 
          ullamcorper ius no, nec ea quodsi invenire. Pri facilisi eleifend ad, ad eos scripta oblique.
        </p>
      </Container>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="flex flex-col items-center text-white">
          <span className="text-sm mb-2">Scroll</span>
          <div className="w-px h-12 bg-white opacity-50"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;

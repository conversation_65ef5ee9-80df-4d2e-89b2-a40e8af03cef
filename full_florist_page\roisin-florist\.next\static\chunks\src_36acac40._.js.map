{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return `$${price}`\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  return phone.replace(/\\s/g, '').replace(/(\\d{3})(\\d{1})(\\d{2})(\\d{4})/, '+$1 $2 $3 $4')\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,AAAC,IAAS,OAAN;AACb;AAEO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,gCAAgC;AAC1E;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/ui/Image.tsx"], "sourcesContent": ["import React from 'react';\nimport NextImage from 'next/image';\nimport { cn } from '@/lib/utils';\n\ninterface ImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  fill?: boolean;\n  priority?: boolean;\n  sizes?: string;\n  unsplashQuery?: string;\n  unsplashSize?: string;\n}\n\nconst Image: React.FC<ImageProps> = ({\n  src,\n  alt,\n  width,\n  height,\n  className,\n  fill = false,\n  priority = false,\n  sizes,\n  unsplashQuery,\n  unsplashSize = '800x600',\n  ...props\n}) => {\n  // Generate Unsplash URL if query is provided\n  const getImageSrc = () => {\n    if (unsplashQuery) {\n      return `https://source.unsplash.com/${unsplashSize}/?${unsplashQuery}`;\n    }\n    return src;\n  };\n\n  const imageProps = {\n    src: getImageSrc(),\n    alt,\n    className: cn('object-cover', className),\n    priority,\n    sizes,\n    ...props,\n  };\n\n  if (fill) {\n    return <NextImage {...imageProps} fill />;\n  }\n\n  return (\n    <NextImage\n      {...imageProps}\n      width={width || 800}\n      height={height || 600}\n    />\n  );\n};\n\nexport default Image;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAeA,MAAM,QAA8B;QAAC,EACnC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,OAAO,KAAK,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,aAAa,EACb,eAAe,SAAS,EACxB,GAAG,OACJ;IACC,6CAA6C;IAC7C,MAAM,cAAc;QAClB,IAAI,eAAe;YACjB,OAAO,AAAC,+BAA+C,OAAjB,cAAa,MAAkB,OAAd;QACzD;QACA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,KAAK;QACL;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC9B;QACA;QACA,GAAG,KAAK;IACV;IAEA,IAAI,MAAM;QACR,qBAAO,6LAAC,gIAAA,CAAA,UAAS;YAAE,GAAG,UAAU;YAAE,IAAI;;;;;;IACxC;IAEA,qBACE,6LAAC,gIAAA,CAAA,UAAS;QACP,GAAG,UAAU;QACd,OAAO,SAAS;QAChB,QAAQ,UAAU;;;;;;AAGxB;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/ui/Container.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n}\n\nconst Container: React.FC<ContainerProps> = ({ \n  children, \n  className, \n  size = 'lg' \n}) => {\n  const sizes = {\n    sm: 'max-w-2xl',\n    md: 'max-w-4xl',\n    lg: 'max-w-6xl',\n    xl: 'max-w-7xl',\n    full: 'max-w-full',\n  };\n\n  return (\n    <div className={cn('mx-auto px-4 sm:px-6 lg:px-8', sizes[size], className)}>\n      {children}\n    </div>\n  );\n};\n\nexport default Container;\n"], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,YAAsC;QAAC,EAC3C,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACZ;IACC,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC,KAAK,CAAC,KAAK,EAAE;kBAC7D;;;;;;AAGP;KAlBM;uCAoBS", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, ...props }, ref) => {\n    const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-green-700 text-white hover:bg-green-800',\n      secondary: 'bg-green-100 text-green-800 hover:bg-green-200',\n      outline: 'border border-green-700 text-green-700 hover:bg-green-50',\n      ghost: 'text-green-700 hover:bg-green-50',\n    };\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 py-2',\n      lg: 'h-12 px-6 text-lg',\n    };\n\n    return (\n      <button\n        className={cn(baseStyles, variants[variant], sizes[size], className)}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAsE;QAArE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IAClE,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC1D,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/data/mockData.ts"], "sourcesContent": ["import { NavItem, Product, TeamMember, Service, ContactInfo, Feature } from '@/types';\n\nexport const navigationItems: NavItem[] = [\n  {\n    label: 'Home',\n    href: '/',\n    children: [\n      { label: 'Orchid', href: '/' },\n      { label: 'Daisy', href: '/daisy' },\n      { label: 'Gardenia', href: '/gardenia' },\n      { label: 'Dahlia', href: '/dahlia' },\n      { label: 'Bluebell', href: '/bluebell' },\n      { label: 'Primrose', href: '/primrose' },\n      { label: 'Landing', href: '/landing' },\n    ],\n  },\n  {\n    label: 'Pages',\n    href: '/pages',\n    children: [\n      { label: 'About Me', href: '/about-me' },\n      { label: 'About Us', href: '/about-us' },\n      { label: 'Contact Us', href: '/contact-us' },\n      { label: 'Get In Touch', href: '/get-in-touch' },\n      { label: 'Our Partners', href: '/our-partners' },\n      { label: 'Our Services', href: '/our-services' },\n      { label: 'Our Team', href: '/our-team' },\n      { label: 'FAQ Page', href: '/faq-page' },\n    ],\n  },\n  {\n    label: 'Portfolio',\n    href: '/portfolio',\n    children: [\n      { label: 'Standard', href: '/portfolio/standard' },\n      { label: 'Gallery', href: '/portfolio/gallery' },\n      { label: 'Gallery Joined', href: '/portfolio/gallery-joined' },\n    ],\n  },\n  {\n    label: 'Blog',\n    href: '/blog',\n    children: [\n      { label: 'Right Sidebar', href: '/blog/right-sidebar' },\n      { label: 'Left Sidebar', href: '/blog/left-sidebar' },\n      { label: 'No Sidebar', href: '/blog/no-sidebar' },\n      { label: 'Masonry Blog', href: '/blog/masonry' },\n    ],\n  },\n  {\n    label: 'Shop',\n    href: '/shop',\n    children: [\n      { label: 'Shop List', href: '/shop' },\n      { label: 'Cart', href: '/cart' },\n      { label: 'Checkout', href: '/checkout' },\n      { label: 'My Account', href: '/my-account' },\n    ],\n  },\n];\n\nexport const contactInfo: ContactInfo = {\n  address: '1087, Bathurst st Toronto ON',\n  phone: '+364 1 65 6365',\n  hours: 'Mon-Thu: 10:00-16:00 Sat-Sun: 10:00-16:00',\n};\n\nexport const products: Product[] = [\n  {\n    id: '1',\n    name: 'White',\n    price: 25,\n    originalPrice: 45,\n    image: '/placeholder-flower-1.jpg',\n    category: 'Anniversaries',\n    isOnSale: true,\n  },\n  {\n    id: '2',\n    name: 'Impatiens',\n    price: 45,\n    image: '/placeholder-flower-2.jpg',\n    category: 'Birthdays',\n    isNew: true,\n  },\n  {\n    id: '3',\n    name: 'Mazus',\n    price: 45,\n    image: '/placeholder-flower-3.jpg',\n    category: 'Gifts',\n  },\n  {\n    id: '4',\n    name: 'Pansies',\n    price: 45,\n    image: '/placeholder-flower-4.jpg',\n    category: 'Anniversaries',\n  },\n  {\n    id: '5',\n    name: 'Dahlia',\n    price: 45,\n    image: '/placeholder-flower-5.jpg',\n    category: 'Birthdays',\n  },\n  {\n    id: '6',\n    name: 'Peony',\n    price: 45,\n    image: '/placeholder-flower-6.jpg',\n    category: 'Gifts',\n  },\n];\n\nexport const teamMembers: TeamMember[] = [\n  {\n    id: '1',\n    name: 'Velva Kopf',\n    role: 'Biologist',\n    image: '/placeholder-team-1.jpg',\n  },\n  {\n    id: '2',\n    name: 'Elizabeth Morris',\n    role: 'Florist',\n    image: '/placeholder-team-2.jpg',\n  },\n  {\n    id: '3',\n    name: 'Blaine Bush',\n    role: 'Photographer',\n    image: '/placeholder-team-3.jpg',\n  },\n];\n\nexport const services: Service[] = [\n  {\n    id: '1',\n    title: 'New arrangements',\n    description: 'Fresh and creative floral arrangements',\n    icon: '/placeholder-icon-1.png',\n  },\n  {\n    id: '2',\n    title: 'Flower specialist',\n    description: 'Expert knowledge in flower care',\n    icon: '/placeholder-icon-2.png',\n  },\n];\n\nexport const features: Feature[] = [\n  {\n    id: '1',\n    title: 'Online Order',\n    description: 'Easy online ordering system',\n    icon: '/placeholder-feature-1.png',\n    link: '#',\n  },\n  {\n    id: '2',\n    title: 'Delivery in 2-4 h',\n    description: 'Fast delivery service',\n    icon: '/placeholder-feature-2.png',\n    link: '#',\n  },\n  {\n    id: '3',\n    title: 'Freshness',\n    description: 'Always fresh flowers',\n    icon: '/placeholder-feature-3.png',\n    link: '#',\n  },\n  {\n    id: '4',\n    title: 'Made by Artists',\n    description: 'Crafted by professional florists',\n    icon: '/placeholder-feature-4.png',\n    link: '#',\n  },\n];\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,kBAA6B;IACxC;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAU,MAAM;YAAI;YAC7B;gBAAE,OAAO;gBAAS,MAAM;YAAS;YACjC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAU,MAAM;YAAU;YACnC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAW,MAAM;YAAW;SACtC;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAc,MAAM;YAAc;YAC3C;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;YAC/C;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;YAC/C;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;YAC/C;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAY,MAAM;YAAY;SACxC;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAY,MAAM;YAAsB;YACjD;gBAAE,OAAO;gBAAW,MAAM;YAAqB;YAC/C;gBAAE,OAAO;gBAAkB,MAAM;YAA4B;SAC9D;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAiB,MAAM;YAAsB;YACtD;gBAAE,OAAO;gBAAgB,MAAM;YAAqB;YACpD;gBAAE,OAAO;gBAAc,MAAM;YAAmB;YAChD;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;SAChD;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAa,MAAM;YAAQ;YACpC;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAc,MAAM;YAAc;SAC5C;IACH;CACD;AAEM,MAAM,cAA2B;IACtC,SAAS;IACT,OAAO;IACP,OAAO;AACT;AAEO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;CACD;AAEM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAEM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAEM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/ProductShowcase.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from '@/components/ui/Image';\nimport Container from '@/components/ui/Container';\nimport Button from '@/components/ui/Button';\nimport { products } from '@/data/mockData';\nimport { formatPrice } from '@/lib/utils';\n\nconst ProductShowcase: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('All');\n  const [sortBy, setSortBy] = useState('Popularity');\n\n  const filters = ['All', 'Anniversaries', 'Birthdays', 'Gifts'];\n  const sortOptions = ['Popularity', 'Average rating', 'Newness', 'Price: Low to High', 'Price: High to Low'];\n\n  const filteredProducts = products.filter(product => \n    activeFilter === 'All' || product.category === activeFilter\n  );\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <Container>\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <p className=\"text-green-700 font-medium mb-2\"><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON></p>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 leading-tight\">\n            The Irresistible World of\n            <br />\n            <span className=\"text-green-700\">Floral Decorations</span>\n          </h2>\n        </div>\n\n        {/* Filters and Sort */}\n        <div className=\"flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0\">\n          {/* Category Filters */}\n          <div className=\"flex flex-wrap gap-2\">\n            {filters.map((filter) => (\n              <button\n                key={filter}\n                onClick={() => setActiveFilter(filter)}\n                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n                  activeFilter === filter\n                    ? 'bg-green-700 text-white'\n                    : 'bg-white text-gray-700 hover:bg-green-50 hover:text-green-700'\n                }`}\n              >\n                {filter}\n              </button>\n            ))}\n          </div>\n\n          {/* Sort Dropdown */}\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-sm font-medium text-gray-700\">Sort By</span>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500\"\n            >\n              {sortOptions.map((option) => (\n                <option key={option} value={option}>\n                  {option}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Product Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {filteredProducts.map((product) => (\n            <div key={product.id} className=\"bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-lg transition-shadow\">\n              {/* Product Image */}\n              <div className=\"relative h-64 overflow-hidden\">\n                <Image\n                  src={product.image}\n                  alt={product.name}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                  unsplashQuery={`${product.name},flower,bouquet`}\n                  unsplashSize=\"400x400\"\n                />\n                \n                {/* Badges */}\n                <div className=\"absolute top-3 left-3 space-y-1\">\n                  {product.isOnSale && (\n                    <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded\">Sale</span>\n                  )}\n                  {product.isNew && (\n                    <span className=\"bg-green-500 text-white text-xs px-2 py-1 rounded\">New</span>\n                  )}\n                </div>\n              </div>\n\n              {/* Product Info */}\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{product.name}</h3>\n                \n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    {product.originalPrice && (\n                      <span className=\"text-gray-400 line-through text-sm\">\n                        {formatPrice(product.originalPrice)}\n                      </span>\n                    )}\n                    <span className=\"text-xl font-bold text-gray-900\">\n                      {formatPrice(product.price)}\n                    </span>\n                  </div>\n                </div>\n\n                <Button className=\"w-full\" variant=\"primary\">\n                  Add to cart\n                </Button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Load More Button */}\n        <div className=\"text-center\">\n          <Button variant=\"outline\" size=\"lg\">\n            Load More Products\n          </Button>\n        </div>\n      </Container>\n    </section>\n  );\n};\n\nexport default ProductShowcase;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,kBAA4B;;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,UAAU;QAAC;QAAO;QAAiB;QAAa;KAAQ;IAC9D,MAAM,cAAc;QAAC;QAAc;QAAkB;QAAW;QAAsB;KAAqB;IAE3G,MAAM,mBAAmB,0HAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,UACvC,iBAAiB,SAAS,QAAQ,QAAQ,KAAK;IAGjD,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC,wIAAA,CAAA,UAAS;;8BAER,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;sCAC/C,6LAAC;4BAAG,WAAU;;gCAA6D;8CAEzE,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;;;;;;;8BAKrC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,AAAC,gEAIX,OAHC,iBAAiB,SACb,4BACA;8CAGL;mCARI;;;;;;;;;;sCAcX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAoC;;;;;;8CACpD,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;8CAET,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4CAAoB,OAAO;sDACzB;2CADU;;;;;;;;;;;;;;;;;;;;;;8BASrB,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;4BAAqB,WAAU;;8CAE9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,IAAI;4CACjB,IAAI;4CACJ,WAAU;4CACV,eAAe,AAAC,GAAe,OAAb,QAAQ,IAAI,EAAC;4CAC/B,cAAa;;;;;;sDAIf,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,QAAQ,kBACf,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;gDAEnE,QAAQ,KAAK,kBACZ,6LAAC;oDAAK,WAAU;8DAAoD;;;;;;;;;;;;;;;;;;8CAM1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C,QAAQ,IAAI;;;;;;sDAEtE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,aAAa,kBACpB,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,aAAa;;;;;;kEAGtC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;;;;;;;;;;;sDAKhC,6LAAC,qIAAA,CAAA,UAAM;4CAAC,WAAU;4CAAS,SAAQ;sDAAU;;;;;;;;;;;;;2BAxCvC,QAAQ,EAAE;;;;;;;;;;8BAiDxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAK;;;;;;;;;;;;;;;;;;;;;;AAO9C;GAxHM;KAAA;uCA0HS", "debugId": null}}]}
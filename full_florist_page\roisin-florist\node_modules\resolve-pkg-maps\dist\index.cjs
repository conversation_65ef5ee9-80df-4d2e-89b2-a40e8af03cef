"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const d=r=>r!==null&&typeof r=="object",s=(r,t)=>Object.assign(new Error(`[${r}]: ${t}`),{code:r}),g="ERR_INVALID_PACKAGE_CONFIG",E="ERR_INVALID_PACKAGE_TARGET",I="ERR_PACKAGE_PATH_NOT_EXPORTED",P="ERR_PACKAGE_IMPORT_NOT_DEFINED",R=/^\d+$/,O=/^(\.{1,2}|node_modules)$/i,u=/\/|\\/;var h=(r=>(r.Export="exports",r.Import="imports",r))(h||{});const f=(r,t,n,o,c)=>{if(t==null)return[];if(typeof t=="string"){const[e,...i]=t.split(u);if(e===".."||i.some(l=>O.test(l)))throw s(E,`Invalid "${r}" target "${t}" defined in the package config`);return[c?t.replace(/\*/g,c):t]}if(Array.isArray(t))return t.flatMap(e=>f(r,e,n,o,c));if(d(t)){for(const e of Object.keys(t)){if(R.test(e))throw s(g,"Cannot contain numeric property keys");if(e==="default"||o.includes(e))return f(r,t[e],n,o,c)}return[]}throw s(E,`Invalid "${r}" target "${t}"`)},a="*",v=(r,t)=>{const n=r.indexOf(a),o=t.indexOf(a);return n===o?t.length>r.length:o>n};function A(r,t){if(!t.includes(a)&&r.hasOwnProperty(t))return[t];let n,o;for(const c of Object.keys(r))if(c.includes(a)){const[e,i,l]=c.split(a);if(l===void 0&&t.startsWith(e)&&t.endsWith(i)){const _=t.slice(e.length,-i.length||void 0);_&&(!n||v(n,c))&&(n=c,o=_)}}return[n,o]}const p=r=>Object.keys(r).reduce((t,n)=>{const o=n===""||n[0]!==".";if(t===void 0||t===o)return o;throw s(g,'"exports" cannot contain some keys starting with "." and some not')},void 0),w=/^\w+:/,m=(r,t,n)=>{if(!r)throw new Error('"exports" is required');t=t===""?".":`./${t}`,(typeof r=="string"||Array.isArray(r)||d(r)&&p(r))&&(r={".":r});const[o,c]=A(r,t),e=f(h.Export,r[o],t,n,c);if(e.length===0)throw s(I,t==="."?'No "exports" main defined':`Package subpath '${t}' is not defined by "exports"`);for(const i of e)if(!i.startsWith("./")&&!w.test(i))throw s(E,`Invalid "exports" target "${i}" defined in the package config`);return e},T=(r,t,n)=>{if(!r)throw new Error('"imports" is required');const[o,c]=A(r,t),e=f(h.Import,r[o],t,n,c);if(e.length===0)throw s(P,`Package import specifier "${t}" is not defined in package`);return e};exports.resolveExports=m,exports.resolveImports=T;

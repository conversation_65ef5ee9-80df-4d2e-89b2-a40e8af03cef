import classNames from 'classnames'
import React from 'react'

export type Size = 'small' | 'medium' | 'large' | 'full'
export type Padding = '0' | '2' | '4' | '6' | '8' | '10' | '12' | '16' | '20'

interface ContainerProps {
  size?: Size
  paddingTop?: Padding
  paddingBottom?: Padding
  className?: string
  children?: React.ReactNode
}

const Container: React.FC<ContainerProps> = ({
  size = 'medium',
  paddingTop = '12',
  paddingBottom = '12',
  className = '',
  children,
}) => {
  const sizeClasses = {
    small: 'max-w-3xl',
    medium: 'max-w-6xl',
    large: 'max-w-7xl',
    full: 'max-w-full',
  }

  const paddingClasses = {
    '0': '',
    '2': 'py-2',
    '4': 'py-4',
    '6': 'py-6',
    '8': 'py-8',
    '10': 'py-10',
    '12': 'py-12',
    '16': 'py-16',
    '20': 'py-20',
  }

  const topPaddingClasses = {
    '0': '',
    '2': 'pt-2',
    '4': 'pt-4',
    '6': 'pt-6',
    '8': 'pt-8',
    '10': 'pt-10',
    '12': 'pt-12',
    '16': 'pt-16',
    '20': 'pt-20',
  }

  const bottomPaddingClasses = {
    '0': '',
    '2': 'pb-2',
    '4': 'pb-4',
    '6': 'pb-6',
    '8': 'pb-8',
    '10': 'pb-10',
    '12': 'pb-12',
    '16': 'pb-16',
    '20': 'pb-20',
  }

  return (
    <div
      className={classNames(
        'mx-auto px-5',
        sizeClasses[size],
        topPaddingClasses[paddingTop],
        bottomPaddingClasses[paddingBottom],
        className
      )}
    >
      {children}
    </div>
  )
}

export default Container

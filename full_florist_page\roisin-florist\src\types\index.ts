// Navigation types
export interface NavItem {
  label: string;
  href: string;
  children?: NavItem[];
}

// Product types
export interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isNew?: boolean;
  isOnSale?: boolean;
}

// Team member types
export interface TeamMember {
  id: string;
  name: string;
  role: string;
  image: string;
  bio?: string;
}

// Service types
export interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
  link?: string;
}

// Contact info types
export interface ContactInfo {
  address: string;
  phone: string;
  hours: string;
}

// Feature types
export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  link?: string;
}

// Image types
export interface ImageData {
  src: string;
  alt: string;
  width?: number;
  height?: number;
}

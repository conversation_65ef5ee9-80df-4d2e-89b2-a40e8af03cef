import { fetchPages, fetchTags, types } from 'react-bricks/rsc'
import config from './config'

const pageTypes: types.IPageType[] = [
  {
    name: 'page',
    pluralName: 'pages',
    defaultLocked: false,
    defaultStatus: types.PageStatus.Published,
    getDefaultContent: () => [],
  },
  {
    name: 'florist-homepage',
    pluralName: 'florist-homepages',
    defaultLocked: false,
    defaultStatus: types.PageStatus.Published,
    getDefaultContent: () => [
      {
        id: 'hero-section',
        type: 'florist-hero',
        props: {
          backgroundColor: { color: '#f0f8f0', className: 'bg-green-25' },
          overlayOpacity: 0.4,
          textAlign: 'center',
          height: 'large',
          title: 'Tomeli<PERSON>',
          subtitle: 'We Create Beauty Inspired by Flora',
          description: 'Discover our exquisite collection of fresh flowers, elegant arrangements, and botanical artistry. Each creation tells a story of natural beauty and craftsmanship.',
          primaryButtonText: 'Shop Arrangements',
          secondaryButtonText: 'Contact Us',
        },
      },
      {
        id: 'services-section',
        type: 'services-highlight',
        props: {
          backgroundColor: { color: '#ffffff', className: 'bg-white' },
          layout: 'horizontal',
          showIcons: true,
          sectionTitle: 'Why Choose Tomelia',
          sectionDescription: 'We\'re committed to providing the finest floral experiences with exceptional service and quality.',
        },
      },
      {
        id: 'products-section',
        type: 'product-grid',
        props: {
          backgroundColor: { color: '#f8f9fa', className: 'bg-gray-50' },
          columns: 3,
          showFilters: true,
          showSorting: true,
          sectionTitle: 'The Irresistible World of Floral Decorations',
          sectionDescription: 'Discover our carefully curated collection of fresh flowers, elegant arrangements, and seasonal bouquets crafted with passion and expertise.',
        },
      },
      {
        id: 'team-section',
        type: 'team-section',
        props: {
          backgroundColor: { color: '#ffffff', className: 'bg-white' },
          layout: 'grid',
          columns: 3,
          sectionSubtitle: 'The best florist crew around',
          sectionTitle: 'Meet the Team That Makes Miracles Happen',
          sectionDescription: 'Our passionate team of floral artists and designers brings years of experience and creativity to every arrangement.',
        },
      },
      {
        id: 'contact-section',
        type: 'contact-info',
        props: {
          backgroundColor: { color: '#f0f8f0', className: 'bg-green-25' },
          layout: 'horizontal',
          showMap: true,
          sectionTitle: 'Visit Our Flower Shop',
          sectionDescription: 'Come visit us in person to see our beautiful flowers and speak with our expert florists.',
        },
      },
    ],
    allowedBlockTypes: [
      'florist-hero',
      'services-highlight',
      'product-grid',
      'team-section',
      'contact-info',
    ],
  },
  {
    name: 'layout',
    pluralName: 'layout',
    defaultLocked: false,
    defaultStatus: types.PageStatus.Published,
    getDefaultContent: () => [],
    isEntity: true,
    allowedBlockTypes: ['header', 'footer'],
  },
]

export default pageTypes

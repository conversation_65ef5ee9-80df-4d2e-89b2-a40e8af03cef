import React from 'react';
import Image from '@/components/ui/Image';
import Container from '@/components/ui/Container';
import Button from '@/components/ui/Button';
import { teamMembers } from '@/data/mockData';

const TeamSection: React.FC = () => {
  return (
    <section className="py-20 bg-gray-50">
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Content */}
          <div className="space-y-6">
            <div>
              <p className="text-green-700 font-medium mb-2">The best florist crew around</p>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                Meet the Team That
                <br />
                <span className="text-green-700">Makes Miracles Happen</span>
              </h2>
            </div>

            {/* Team Members */}
            <div className="space-y-6">
              {teamMembers.map((member, index) => (
                <div key={member.id} className="flex items-center space-x-4 p-4 bg-white rounded-lg shadow-sm">
                  <div className="relative w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
                    <Image
                      src={member.image}
                      alt={member.name}
                      fill
                      className="object-cover"
                      unsplashQuery={`${member.role},professional,portrait`}
                      unsplashSize="200x200"
                    />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{member.name}</h3>
                    <p className="text-green-700 text-sm">{member.role}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Section */}
            <div className="bg-green-700 text-white p-6 rounded-lg">
              <h3 className="text-2xl font-bold mb-2">Sale 50%</h3>
              <p className="text-green-100 text-sm mb-4">weekly discount</p>
              <h4 className="text-xl font-semibold mb-3">Beauty You Are Sure to Treasure</h4>
              <p className="text-green-100 mb-4 text-sm">
                Lorem ipsum dolor sit amet, pri omnium verterem id, sit labore dicunt an, ea civibus.
              </p>
              <Button variant="secondary" className="bg-white text-green-700 hover:bg-gray-100">
                View more
              </Button>
            </div>
          </div>

          {/* Right Side - Video/Image */}
          <div className="relative">
            <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-lg">
              <Image
                src="/team-video-thumbnail.jpg"
                alt="Team working with flowers"
                fill
                className="object-cover"
                unsplashQuery="florist,team,working,flowers"
                unsplashSize="600x800"
              />
              
              {/* Video Play Button */}
              <div className="absolute inset-0 flex items-center justify-center">
                <button className="w-20 h-20 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all group">
                  <svg className="w-8 h-8 text-green-700 ml-1 group-hover:scale-110 transition-transform" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
              </div>
              
              {/* Video Label */}
              <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
                Video
              </div>
            </div>

            {/* Floating Quote */}
            <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-lg shadow-lg max-w-xs">
              <p className="text-gray-700 text-sm italic mb-2">
                "Suzane Murray brings years of expertise to every arrangement"
              </p>
              <p className="text-green-700 font-medium text-sm">- Client Review</p>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default TeamSection;

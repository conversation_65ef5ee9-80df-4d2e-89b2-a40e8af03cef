{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/ui/Image.tsx"], "sourcesContent": ["import React from 'react';\nimport NextImage from 'next/image';\nimport { cn } from '@/lib/utils';\n\ninterface ImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  fill?: boolean;\n  priority?: boolean;\n  sizes?: string;\n  unsplashQuery?: string;\n  unsplashSize?: string;\n}\n\nconst Image: React.FC<ImageProps> = ({\n  src,\n  alt,\n  width,\n  height,\n  className,\n  fill = false,\n  priority = false,\n  sizes,\n  unsplashQuery,\n  unsplashSize = '800x600',\n  ...props\n}) => {\n  // Generate Unsplash URL if query is provided\n  const getImageSrc = () => {\n    if (unsplashQuery) {\n      return `https://source.unsplash.com/${unsplashSize}/?${unsplashQuery}`;\n    }\n    return src;\n  };\n\n  const imageProps = {\n    src: getImageSrc(),\n    alt,\n    className: cn('object-cover', className),\n    priority,\n    sizes,\n    ...props,\n  };\n\n  if (fill) {\n    return <NextImage {...imageProps} fill />;\n  }\n\n  return (\n    <NextImage\n      {...imageProps}\n      width={width || 800}\n      height={height || 600}\n    />\n  );\n};\n\nexport default Image;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAeA,MAAM,QAA8B,CAAC,EACnC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,OAAO,KAAK,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,aAAa,EACb,eAAe,SAAS,EACxB,GAAG,OACJ;IACC,6CAA6C;IAC7C,MAAM,cAAc;QAClB,IAAI,eAAe;YACjB,OAAO,CAAC,4BAA4B,EAAE,aAAa,EAAE,EAAE,eAAe;QACxE;QACA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,KAAK;QACL;QACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC9B;QACA;QACA,GAAG,KAAK;IACV;IAEA,IAAI,MAAM;QACR,qBAAO,8OAAC,6HAAA,CAAA,UAAS;YAAE,GAAG,UAAU;YAAE,IAAI;;;;;;IACxC;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAS;QACP,GAAG,UAAU;QACd,OAAO,SAAS;QAChB,QAAQ,UAAU;;;;;;AAGxB;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { contactInfo } from '@/data/mockData';\nimport Image from '@/components/ui/Image';\nimport Container from '@/components/ui/Container';\n\nconst HeroSection: React.FC = () => {\n  return (\n    <section className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0 z-0\">\n        <Image\n          src=\"/hero-bg.jpg\"\n          alt=\"Beautiful flower arrangement\"\n          fill\n          priority\n          className=\"object-cover\"\n          unsplashQuery=\"flowers,bouquet,florist\"\n          unsplashSize=\"1920x1080\"\n        />\n        <div className=\"absolute inset-0 bg-black bg-opacity-30\" />\n      </div>\n\n      {/* Contact Info Cards */}\n      <div className=\"absolute top-20 left-8 right-8 z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 max-w-6xl mx-auto\">\n          {[...Array(4)].map((_, index) => (\n            <div\n              key={index}\n              className=\"bg-white bg-opacity-90 backdrop-blur-sm p-4 rounded-lg shadow-lg text-center\"\n            >\n              <div className=\"text-sm font-medium text-gray-800 mb-1\">\n                {contactInfo.address}\n              </div>\n              <div className=\"text-xs text-gray-600\">\n                {contactInfo.phone}\n              </div>\n              <div className=\"text-xs text-gray-600 mt-1\">\n                {contactInfo.hours}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <Container className=\"relative z-10 text-center text-white\">\n        <h1 className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\">\n          We Produce Beauty\n          <br />\n          <span className=\"text-green-300\">Inspired by Flora</span>\n        </h1>\n        <p className=\"text-xl md:text-2xl mb-8 max-w-2xl mx-auto leading-relaxed\">\n          Lorem ipsum dolor sit amet, pri autem nemore bonorum te. Autem fierent \n          ullamcorper ius no, nec ea quodsi invenire. Pri facilisi eleifend ad, ad eos scripta oblique.\n        </p>\n      </Container>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\">\n        <div className=\"flex flex-col items-center text-white\">\n          <span className=\"text-sm mb-2\">Scroll</span>\n          <div className=\"w-px h-12 bg-white opacity-50\"></div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,MAAM,cAAwB;IAC5B,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,QAAQ;wBACR,WAAU;wBACV,eAAc;wBACd,cAAa;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,uHAAA,CAAA,cAAW,CAAC,OAAO;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;8CACZ,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;8CAEpB,8OAAC;oCAAI,WAAU;8CACZ,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;2BAVf;;;;;;;;;;;;;;;0BAkBb,8OAAC,qIAAA,CAAA,UAAS;gBAAC,WAAU;;kCACnB,8OAAC;wBAAG,WAAU;;4BAAoD;0CAEhE,8OAAC;;;;;0CACD,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;kCAEnC,8OAAC;wBAAE,WAAU;kCAA6D;;;;;;;;;;;;0BAO5E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAe;;;;;;sCAC/B,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/AboutSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from '@/components/ui/Image';\nimport Container from '@/components/ui/Container';\n\nconst AboutSection: React.FC = () => {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <Container>\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Text Content */}\n          <div className=\"space-y-6\">\n            <div className=\"space-y-2\">\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 leading-tight\">\n                We Produce Beauty\n                <br />\n                <span className=\"text-green-700\">Inspired by Flora</span>\n              </h2>\n            </div>\n            \n            <p className=\"text-lg text-gray-600 leading-relaxed\">\n              Lorem ipsum dolor sit amet, pri autem nemore bonorum te. Autem fierent \n              ullamcorper ius no, nec ea quodsi invenire. Pri facilisi eleifend ad, \n              ad eos scripta oblique. Vix cu oratio.\n            </p>\n\n            {/* Owner Quote */}\n            <div className=\"bg-white p-6 rounded-lg shadow-sm border-l-4 border-green-600\">\n              <p className=\"text-gray-700 italic mb-3\">\n                \"We believe that every flower tells a story, and every arrangement \n                captures a moment of pure beauty.\"\n              </p>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                  <span className=\"text-green-700 font-semibold text-sm\">CM</span>\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">Camila Menson</p>\n                  <p className=\"text-sm text-gray-600\">Owner of Roisin Store</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Image */}\n          <div className=\"relative\">\n            <div className=\"relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-lg\">\n              <Image\n                src=\"/about-image.jpg\"\n                alt=\"Florist arranging beautiful flowers\"\n                fill\n                className=\"object-cover\"\n                unsplashQuery=\"florist,woman,flowers,arranging\"\n                unsplashSize=\"600x800\"\n              />\n            </div>\n            \n            {/* Floating Card */}\n            <div className=\"absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-lg\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-700 mb-1\">15+</div>\n                <div className=\"text-sm text-gray-600\">Years Experience</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </section>\n  );\n};\n\nexport default AboutSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,eAAyB;IAC7B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC,qIAAA,CAAA,UAAS;sBACR,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;wCAA6D;sDAEzE,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAAiB;;;;;;;;;;;;;;;;;0CAIrC,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAOrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDAIzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;0DAEzD,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA8B;;;;;;kEAC3C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,eAAc;oCACd,cAAa;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;sDACxD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;uCAEe", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/ServicesSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from '@/components/ui/Image';\nimport Container from '@/components/ui/Container';\nimport { services } from '@/data/mockData';\n\nconst ServicesSection: React.FC = () => {\n  return (\n    <section className=\"py-20 bg-white\">\n      <Container>\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16\">\n          {/* Left Side - Images */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-4\">\n              <div className=\"relative h-48 rounded-lg overflow-hidden\">\n                <Image\n                  src=\"/service-1.jpg\"\n                  alt=\"New arrangements\"\n                  fill\n                  className=\"object-cover\"\n                  unsplashQuery=\"flower,arrangement,bouquet\"\n                  unsplashSize=\"400x300\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\" />\n                <div className=\"absolute bottom-4 left-4 text-white\">\n                  <h3 className=\"font-semibold\">New arrangements</h3>\n                </div>\n              </div>\n              \n              <div className=\"relative h-32 rounded-lg overflow-hidden\">\n                <Image\n                  src=\"/service-2.jpg\"\n                  alt=\"Flower specialist\"\n                  fill\n                  className=\"object-cover\"\n                  unsplashQuery=\"florist,specialist,flowers\"\n                  unsplashSize=\"400x200\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\" />\n                <div className=\"absolute bottom-3 left-3 text-white\">\n                  <h3 className=\"font-semibold text-sm\">Flower specialist</h3>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"relative h-80 rounded-lg overflow-hidden\">\n              <Image\n                src=\"/service-main.jpg\"\n                alt=\"Ottawa Florist\"\n                fill\n                className=\"object-cover\"\n                unsplashQuery=\"florist,shop,flowers,ottawa\"\n                unsplashSize=\"400x500\"\n              />\n            </div>\n          </div>\n\n          {/* Right Side - Content */}\n          <div className=\"space-y-6\">\n            <div>\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 leading-tight mb-4\">\n                Ottawa Florist\n                <br />\n                <span className=\"text-green-700\">& Plant Lover</span>\n              </h2>\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                Lorem ipsum dolor sit amet summ dolore, eu omnes mnesarchum eosatsimuscum primis.\n              </p>\n            </div>\n\n            {/* Service Cards */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-4 p-4 bg-gray-50 rounded-lg\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                  <span className=\"text-green-700 font-bold\">01</span>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-1\">Suzan Mary</h3>\n                  <p className=\"text-sm text-gray-600\">No. 01 trees specialist</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4 p-4 bg-gray-50 rounded-lg\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                  <span className=\"text-green-700 font-bold\">02</span>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-1\">Malva Dali</h3>\n                  <p className=\"text-sm text-gray-600\">No. 02 trees specialist</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section - Special Offers */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"relative h-64 rounded-lg overflow-hidden group cursor-pointer\">\n            <Image\n              src=\"/offer-1.jpg\"\n              alt=\"Perfect gifts\"\n              fill\n              className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n              unsplashQuery=\"gift,flowers,bouquet\"\n              unsplashSize=\"400x400\"\n            />\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\" />\n            <div className=\"absolute bottom-6 left-6 text-white\">\n              <h3 className=\"text-xl font-semibold mb-1\">Perfect gifts</h3>\n              <p className=\"text-green-300 font-medium\">On sale</p>\n              <p className=\"text-sm opacity-90\">Fraser, specializes</p>\n            </div>\n          </div>\n\n          <div className=\"relative h-64 rounded-lg overflow-hidden group cursor-pointer\">\n            <Image\n              src=\"/offer-2.jpg\"\n              alt=\"House plants\"\n              fill\n              className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n              unsplashQuery=\"house,plants,indoor\"\n              unsplashSize=\"400x400\"\n            />\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\" />\n            <div className=\"absolute bottom-6 left-6 text-white\">\n              <h3 className=\"text-xl font-semibold mb-1\">House plants</h3>\n              <p className=\"text-green-300 font-medium\">20% discount</p>\n            </div>\n          </div>\n\n          <div className=\"relative h-64 rounded-lg overflow-hidden group cursor-pointer\">\n            <Image\n              src=\"/offer-3.jpg\"\n              alt=\"Flower decoration\"\n              fill\n              className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n              unsplashQuery=\"flower,decoration,wedding\"\n              unsplashSize=\"400x400\"\n            />\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\" />\n            <div className=\"absolute bottom-6 left-6 text-white\">\n              <h3 className=\"text-xl font-semibold mb-1\">Flower decoration</h3>\n              <p className=\"text-green-300 font-medium\">Free delivery</p>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAGA,MAAM,kBAA4B;IAChC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC,qIAAA,CAAA,UAAS;;8BACR,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,eAAc;oDACd,cAAa;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,WAAU;kEAAgB;;;;;;;;;;;;;;;;;sDAIlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,eAAc;oDACd,cAAa;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,eAAc;wCACd,cAAa;;;;;;;;;;;;;;;;;sCAMnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAkE;8DAE9E,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;;;;;;;sDAEnC,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAMvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;8DAE7C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;8DAE7C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,eAAc;oCACd,cAAa;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;sCAItC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,eAAc;oCACd,cAAa;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAI9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,eAAc;oCACd,cAAa;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD;uCAEe", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/ProductShowcase.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ProductShowcase.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ProductShowcase.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/ProductShowcase.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ProductShowcase.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ProductShowcase.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/FeaturesSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Container from '@/components/ui/Container';\nimport { features } from '@/data/mockData';\n\nconst FeaturesSection: React.FC = () => {\n  const featureIcons = {\n    'Online Order': (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>\n    ),\n    'Delivery in 2-4 h': (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>\n    ),\n    'Freshness': (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\" />\n      </svg>\n    ),\n    'Made by Artists': (\n      <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z\" />\n      </svg>\n    ),\n  };\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <Container>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature) => (\n            <div key={feature.id} className=\"text-center group\">\n              {/* Icon */}\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center text-green-700 group-hover:bg-green-700 group-hover:text-white transition-colors\">\n                {featureIcons[feature.title as keyof typeof featureIcons]}\n              </div>\n              \n              {/* Content */}\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 text-sm mb-3\">\n                {feature.description}\n              </p>\n              \n              {/* Link */}\n              {feature.link && (\n                <a\n                  href={feature.link}\n                  className=\"text-green-700 text-sm font-medium hover:text-green-800 transition-colors\"\n                >\n                  More info →\n                </a>\n              )}\n            </div>\n          ))}\n        </div>\n      </Container>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,kBAA4B;IAChC,MAAM,eAAe;QACnB,8BACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,mCACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,2BACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,iCACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;IAG3E;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC,qIAAA,CAAA,UAAS;sBACR,cAAA,8OAAC;gBAAI,WAAU;0BACZ,uHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,wBACb,8OAAC;wBAAqB,WAAU;;0CAE9B,8OAAC;gCAAI,WAAU;0CACZ,YAAY,CAAC,QAAQ,KAAK,CAA8B;;;;;;0CAI3D,8OAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;0CAEhB,8OAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;4BAIrB,QAAQ,IAAI,kBACX,8OAAC;gCACC,MAAM,QAAQ,IAAI;gCAClB,WAAU;0CACX;;;;;;;uBAnBK,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;AA6BhC;uCAEe", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, ...props }, ref) => {\n    const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-green-700 text-white hover:bg-green-800',\n      secondary: 'bg-green-100 text-green-800 hover:bg-green-200',\n      outline: 'border border-green-700 text-green-700 hover:bg-green-50',\n      ghost: 'text-green-700 hover:bg-green-50',\n    };\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 py-2',\n      lg: 'h-12 px-6 text-lg',\n    };\n\n    return (\n      <button\n        className={cn(baseStyles, variants[variant], sizes[size], className)}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpE,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC1D,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/TeamSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from '@/components/ui/Image';\nimport Container from '@/components/ui/Container';\nimport Button from '@/components/ui/Button';\nimport { teamMembers } from '@/data/mockData';\n\nconst TeamSection: React.FC = () => {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <Container>\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Side - Content */}\n          <div className=\"space-y-6\">\n            <div>\n              <p className=\"text-green-700 font-medium mb-2\">The best florist crew around</p>\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 leading-tight\">\n                Meet the Team That\n                <br />\n                <span className=\"text-green-700\">Makes Miracles Happen</span>\n              </h2>\n            </div>\n\n            {/* Team Members */}\n            <div className=\"space-y-6\">\n              {teamMembers.map((member, index) => (\n                <div key={member.id} className=\"flex items-center space-x-4 p-4 bg-white rounded-lg shadow-sm\">\n                  <div className=\"relative w-16 h-16 rounded-full overflow-hidden flex-shrink-0\">\n                    <Image\n                      src={member.image}\n                      alt={member.name}\n                      fill\n                      className=\"object-cover\"\n                      unsplashQuery={`${member.role},professional,portrait`}\n                      unsplashSize=\"200x200\"\n                    />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900\">{member.name}</h3>\n                    <p className=\"text-green-700 text-sm\">{member.role}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* CTA Section */}\n            <div className=\"bg-green-700 text-white p-6 rounded-lg\">\n              <h3 className=\"text-2xl font-bold mb-2\">Sale 50%</h3>\n              <p className=\"text-green-100 text-sm mb-4\">weekly discount</p>\n              <h4 className=\"text-xl font-semibold mb-3\">Beauty You Are Sure to Treasure</h4>\n              <p className=\"text-green-100 mb-4 text-sm\">\n                Lorem ipsum dolor sit amet, pri omnium verterem id, sit labore dicunt an, ea civibus.\n              </p>\n              <Button variant=\"secondary\" className=\"bg-white text-green-700 hover:bg-gray-100\">\n                View more\n              </Button>\n            </div>\n          </div>\n\n          {/* Right Side - Video/Image */}\n          <div className=\"relative\">\n            <div className=\"relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-lg\">\n              <Image\n                src=\"/team-video-thumbnail.jpg\"\n                alt=\"Team working with flowers\"\n                fill\n                className=\"object-cover\"\n                unsplashQuery=\"florist,team,working,flowers\"\n                unsplashSize=\"600x800\"\n              />\n              \n              {/* Video Play Button */}\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <button className=\"w-20 h-20 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all group\">\n                  <svg className=\"w-8 h-8 text-green-700 ml-1 group-hover:scale-110 transition-transform\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M8 5v14l11-7z\"/>\n                  </svg>\n                </button>\n              </div>\n              \n              {/* Video Label */}\n              <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm\">\n                Video\n              </div>\n            </div>\n\n            {/* Floating Quote */}\n            <div className=\"absolute -bottom-6 -right-6 bg-white p-6 rounded-lg shadow-lg max-w-xs\">\n              <p className=\"text-gray-700 text-sm italic mb-2\">\n                \"Suzane Murray brings years of expertise to every arrangement\"\n              </p>\n              <p className=\"text-green-700 font-medium text-sm\">- Client Review</p>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </section>\n  );\n};\n\nexport default TeamSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAwB;IAC5B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC,qIAAA,CAAA,UAAS;sBACR,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAC/C,8OAAC;wCAAG,WAAU;;4CAA6D;0DAEzE,8OAAC;;;;;0DACD,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;0CACZ,uHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,UAAK;oDACJ,KAAK,OAAO,KAAK;oDACjB,KAAK,OAAO,IAAI;oDAChB,IAAI;oDACJ,WAAU;oDACV,eAAe,GAAG,OAAO,IAAI,CAAC,sBAAsB,CAAC;oDACrD,cAAa;;;;;;;;;;;0DAGjB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B,OAAO,IAAI;;;;;;kEACxD,8OAAC;wDAAE,WAAU;kEAA0B,OAAO,IAAI;;;;;;;;;;;;;uCAb5C,OAAO,EAAE;;;;;;;;;;0CAoBvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAG3C,8OAAC,kIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAY,WAAU;kDAA4C;;;;;;;;;;;;;;;;;;kCAOtF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,eAAc;wCACd,cAAa;;;;;;kDAIf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;sDAChB,cAAA,8OAAC;gDAAI,WAAU;gDAAyE,MAAK;gDAAe,SAAQ;0DAClH,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;kDAMd,8OAAC;wCAAI,WAAU;kDAAuF;;;;;;;;;;;;0CAMxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;kDAGjD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhE;uCAEe", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/app/page.tsx"], "sourcesContent": ["import HeroSection from '@/components/sections/HeroSection';\nimport AboutSection from '@/components/sections/AboutSection';\nimport ServicesSection from '@/components/sections/ServicesSection';\nimport ProductShowcase from '@/components/sections/ProductShowcase';\nimport FeaturesSection from '@/components/sections/FeaturesSection';\nimport TeamSection from '@/components/sections/TeamSection';\n\nexport default function Home() {\n  return (\n    <main>\n      <HeroSection />\n      <AboutSection />\n      <ServicesSection />\n      <ProductShowcase />\n      <FeaturesSection />\n      <TeamSection />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,6IAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,8IAAA,CAAA,UAAY;;;;;0BACb,8OAAC,iJAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,iJAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,iJAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,6IAAA,CAAA,UAAW;;;;;;;;;;;AAGlB", "debugId": null}}]}
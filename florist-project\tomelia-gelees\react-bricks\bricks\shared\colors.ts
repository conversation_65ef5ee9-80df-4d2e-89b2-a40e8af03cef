export const bgColors = {
  WHITE: { value: { color: '#ffffff', className: 'bg-white' }, label: 'White' },
  LIGHT_GRAY: { value: { color: '#f8fafc', className: 'bg-slate-50' }, label: 'Light Gray' },
  GRAY: { value: { color: '#f1f5f9', className: 'bg-slate-100' }, label: 'Gray' },
  ROSE: { value: { color: '#fdf2f8', className: 'bg-rose-50' }, label: 'Rose' },
  ORANGE: { value: { color: '#fff7ed', className: 'bg-orange-50' }, label: 'Orange' },
  AMBER: { value: { color: '#fffbeb', className: 'bg-amber-50' }, label: 'Amber' },
  YELLOW: { value: { color: '#fefce8', className: 'bg-yellow-50' }, label: 'Yellow' },
  LIME: { value: { color: '#f7fee7', className: 'bg-lime-50' }, label: 'Lime' },
  GREEN: { value: { color: '#f0fdf4', className: 'bg-green-50' }, label: 'Green' },
  EMERALD: { value: { color: '#ecfdf5', className: 'bg-emerald-50' }, label: 'Emerald' },
  TEAL: { value: { color: '#f0fdfa', className: 'bg-teal-50' }, label: 'Teal' },
  CYAN: { value: { color: '#ecfeff', className: 'bg-cyan-50' }, label: 'Cyan' },
  SKY: { value: { color: '#f0f9ff', className: 'bg-sky-50' }, label: 'Sky' },
  BLUE: { value: { color: '#eff6ff', className: 'bg-blue-50' }, label: 'Blue' },
  INDIGO: { value: { color: '#eef2ff', className: 'bg-indigo-50' }, label: 'Indigo' },
  VIOLET: { value: { color: '#f5f3ff', className: 'bg-violet-50' }, label: 'Violet' },
  PURPLE: { value: { color: '#faf5ff', className: 'bg-purple-50' }, label: 'Purple' },
  FUCHSIA: { value: { color: '#fdf4ff', className: 'bg-fuchsia-50' }, label: 'Fuchsia' },
  PINK: { value: { color: '#fdf2f8', className: 'bg-pink-50' }, label: 'Pink' },
}

export const textColors = {
  GRAY_800: 'text-gray-800',
  GRAY_700: 'text-gray-700',
  GRAY_600: 'text-gray-600',
  WHITE: 'text-white',
}

export const highlightTextColors = {
  PINK: { value: { color: '#ec4899', className: 'text-pink-500' }, label: 'Pink' },
  VIOLET: { value: { color: '#8b5cf6', className: 'text-violet-500' }, label: 'Violet' },
  CYAN: { value: { color: '#06b6d4', className: 'text-cyan-500' }, label: 'Cyan' },
  LIME: { value: { color: '#84cc16', className: 'text-lime-500' }, label: 'Lime' },
  SKY: { value: { color: '#0ea5e9', className: 'text-sky-500' }, label: 'Sky' },
  ROSE: { value: { color: '#f43f5e', className: 'text-rose-500' }, label: 'Rose' },
  GREEN: { value: { color: '#22c55e', className: 'text-green-500' }, label: 'Green' },
}

export const buttonColors = {
  SKY: { value: { color: '#0ea5e9', className: 'bg-sky-500 hover:bg-sky-600 text-white' }, label: 'Sky' },
  VIOLET: { value: { color: '#8b5cf6', className: 'bg-violet-500 hover:bg-violet-600 text-white' }, label: 'Violet' },
  PINK: { value: { color: '#ec4899', className: 'bg-pink-500 hover:bg-pink-600 text-white' }, label: 'Pink' },
  GREEN: { value: { color: '#22c55e', className: 'bg-green-500 hover:bg-green-600 text-white' }, label: 'Green' },
  ROSE: { value: { color: '#f43f5e', className: 'bg-rose-500 hover:bg-rose-600 text-white' }, label: 'Rose' },
}

export const gradients = {
  NONE: { value: 'none', label: 'None', className: '' },
  DAWN: { 
    value: 'dawn', 
    label: 'Dawn', 
    className: 'bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500' 
  },
  OCEAN: { 
    value: 'ocean', 
    label: 'Ocean', 
    className: 'bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500' 
  },
  FOREST: { 
    value: 'forest', 
    label: 'Forest', 
    className: 'bg-gradient-to-r from-green-400 to-blue-500' 
  },
}

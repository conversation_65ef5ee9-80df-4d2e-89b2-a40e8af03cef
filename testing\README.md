# React Bricks starter with Next.js using App Router with Tailwind CSS and React Bricks UI

Kick-start your project with this boilerplate for a complete Next.js website based on [React Bricks](https://reactbricks.com), with both the front-end and admin dashboard.

## 🚀 Quick start

We suggest that you use the CLI and choose this starter.  
In this way you will have the credentials already set up in a `.env.local` file:

```bash
npx create-reactbricks-app
# or
yarn create reactbricks-app
```

Otherwise you can directly clone this repo:

```bash
git clone https://github.com/reactbricks/nextjs-rsc-starter-reactbricks-tailwind your-project
```

## 📖 Documentation

Please, read our documentation at [Reactbricks.com](https://reactbricks.com).

.linkMenuItem {
  display: none;
  justify-content: center;
  align-items: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-radius: 5px;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  color: rgb(75 85 99);
}

.linkMenuItemActive {
  color: rgb(2 132 199);
  background-color: rgb(14 165 233 / 0.1);
}

:global(.dark) .linkMenuItemActive {
  color: white;
  background-color: rgb(14 165 233 / 0.3);
}

:global(.dark) .linkMenuItem {
  color: white;
}

.linkMenuItem:hover {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

:global(.dark) .linkMenuItem:hover {
  color: white;
  background-color: rgb(14 165 233 / 0.4);
}

.linkHamburgerMenuItem {
  display: block;
  font-size: 0.875rem;
  line-height: 1.25rem;
  margin-bottom: 0.75rem;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  color: rgb(31 41 55);
}

/*:global(.dark) .linkHamburgerMenuItem {
  color: white;
}*/

.linkHamburgerMenuItem:hover {
  color: rgb(2 132 199);
}

/*:global(.dark) .linkHamburgerMenuItem:hover {
  color: rgb(14 165 233);
}*/

.containerLinkItemWithSubItems {
  display: none;
  position: relative;
}

.buttonLinkItemWithSubItems {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 700;
  line-height: 1.25rem;

  color: rgb(75 85 99);
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-radius: 5px;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  background-color: transparent;
}

:global(.dark) .buttonLinkItemWithSubItems {
  color: white;
}

.buttonLinkItemWithSubItems:hover {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

.buttonLinkItemWithSubItems:focus {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

:global(.dark) .buttonLinkItemWithSubItems:hover {
  color: white;
  background-color: rgb(14 165 233 / 0.4);
}
:global(.dark) .buttonLinkItemWithSubItems:focus {
  color: white;
  background-color: rgb(14 165 233 / 0.4);
}

.buttonLinkItemWithSubItemsOpen {
  background-color: rgb(14 165 233 / 0.4);
  color: rgb(2 132 199);
}

:global(.dark) .buttonLinkItemWithSubItemsOpen {
  color: white;
}

.buttonTextActive {
  color: rgb(2 132 199);
  background-color: rgb(14 165 233 / 0.1);
}

:global(.dark) .buttonTextActive {
  color: rgb(56 189 248);
}

.svgClass {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-left: 5px;
}

.containerSubmenuItemsOpen {
  position: absolute;
  top: 2.25rem;
  z-index: 10;
  width: 16rem;
  background-color: white;
  padding: 0.75rem;
  border-width: 1px;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/*:global(.dark) .containerSubmenuItemsOpen {
  background-color: rgb(17 24 39);
  border-color: rgb(156 163 175);
}*/

.containerSubmenuItems {
  margin-bottom: 1.5rem;
}

.containerLinkText {
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 800;
  text-transform: uppercase;
  color: rgb(107 114 128);
  letter-spacing: 0.35rem;
  margin-bottom: 1rem;
}

/*:global(.dark) .containerLinkText {
  color: white;
}*/

@media (min-width: 1024px) {
  .linkMenuItem {
    display: inline-flex;
  }
  .linkHamburgerMenuItem {
    display: none;
  }
  .containerLinkItemWithSubItems {
    display: block;
  }
  .containerSubmenuItems {
    display: none;
  }
}

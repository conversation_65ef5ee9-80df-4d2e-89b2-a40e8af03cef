import { types } from 'react-bricks/rsc'

import FloristHero from './FloristHero'
import ProductCard from './ProductCard'
import ProductGrid from './ProductGrid'
import ServicesHighlight, { ServiceItem } from './ServicesHighlight'
import TeamSection, { TeamMember } from './TeamSection'
import ContactInfo from './ContactInfo'

const florist: types.Brick<any>[] = [
  FloristHero,
  ProductCard,
  ProductGrid,
  ServicesHighlight,
  ServiceItem,
  TeamSection,
  TeamMember,
  ContactInfo,
]

export default florist

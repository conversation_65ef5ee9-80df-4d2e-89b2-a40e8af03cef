{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/ui/Image.tsx"], "sourcesContent": ["import React from 'react';\nimport NextImage from 'next/image';\nimport { cn } from '@/lib/utils';\n\ninterface ImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  fill?: boolean;\n  priority?: boolean;\n  sizes?: string;\n  unsplashQuery?: string;\n  unsplashSize?: string;\n}\n\nconst Image: React.FC<ImageProps> = ({\n  src,\n  alt,\n  width,\n  height,\n  className,\n  fill = false,\n  priority = false,\n  sizes,\n  unsplashQuery,\n  unsplashSize = '800x600',\n  ...props\n}) => {\n  // Generate Unsplash URL if query is provided\n  const getImageSrc = () => {\n    if (unsplashQuery) {\n      return `https://source.unsplash.com/${unsplashSize}/?${unsplashQuery}`;\n    }\n    return src;\n  };\n\n  const imageProps = {\n    src: getImageSrc(),\n    alt,\n    className: cn('object-cover', className),\n    priority,\n    sizes,\n    ...props,\n  };\n\n  if (fill) {\n    return <NextImage {...imageProps} fill />;\n  }\n\n  return (\n    <NextImage\n      {...imageProps}\n      width={width || 800}\n      height={height || 600}\n    />\n  );\n};\n\nexport default Image;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAeA,MAAM,QAA8B;QAAC,EACnC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,OAAO,KAAK,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,aAAa,EACb,eAAe,SAAS,EACxB,GAAG,OACJ;IACC,6CAA6C;IAC7C,MAAM,cAAc;QAClB,IAAI,eAAe;YACjB,OAAO,AAAC,+BAA+C,OAAjB,cAAa,MAAkB,OAAd;QACzD;QACA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,KAAK;QACL;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC9B;QACA;QACA,GAAG,KAAK;IACV;IAEA,IAAI,MAAM;QACR,qBAAO,6LAAC,gIAAA,CAAA,UAAS;YAAE,GAAG,UAAU;YAAE,IAAI;;;;;;IACxC;IAEA,qBACE,6LAAC,gIAAA,CAAA,UAAS;QACP,GAAG,UAAU;QACd,OAAO,SAAS;QAChB,QAAQ,UAAU;;;;;;AAGxB;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/sections/ProductShowcase.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from '@/components/ui/Image';\nimport Container from '@/components/ui/Container';\nimport Button from '@/components/ui/Button';\nimport { products } from '@/data/mockData';\nimport { formatPrice } from '@/lib/utils';\n\nconst ProductShowcase: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState('All');\n  const [sortBy, setSortBy] = useState('Popularity');\n\n  const filters = ['All', 'Anniversaries', 'Birthdays', 'Gifts'];\n  const sortOptions = ['Popularity', 'Average rating', 'Newness', 'Price: Low to High', 'Price: High to Low'];\n\n  const filteredProducts = products.filter(product => \n    activeFilter === 'All' || product.category === activeFilter\n  );\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <Container>\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <p className=\"text-green-700 font-medium mb-2\"><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON></p>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 leading-tight\">\n            The Irresistible World of\n            <br />\n            <span className=\"text-green-700\">Floral Decorations</span>\n          </h2>\n        </div>\n\n        {/* Filters and Sort */}\n        <div className=\"flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0\">\n          {/* Category Filters */}\n          <div className=\"flex flex-wrap gap-2\">\n            {filters.map((filter) => (\n              <button\n                key={filter}\n                onClick={() => setActiveFilter(filter)}\n                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n                  activeFilter === filter\n                    ? 'bg-green-700 text-white'\n                    : 'bg-white text-gray-700 hover:bg-green-50 hover:text-green-700'\n                }`}\n              >\n                {filter}\n              </button>\n            ))}\n          </div>\n\n          {/* Sort Dropdown */}\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-sm font-medium text-gray-700\">Sort By</span>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500\"\n            >\n              {sortOptions.map((option) => (\n                <option key={option} value={option}>\n                  {option}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Product Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {filteredProducts.map((product) => (\n            <div key={product.id} className=\"bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-lg transition-shadow\">\n              {/* Product Image */}\n              <div className=\"relative h-64 overflow-hidden\">\n                <Image\n                  src={product.image}\n                  alt={product.name}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                  unsplashQuery={`${product.name},flower,bouquet`}\n                  unsplashSize=\"400x400\"\n                />\n                \n                {/* Badges */}\n                <div className=\"absolute top-3 left-3 space-y-1\">\n                  {product.isOnSale && (\n                    <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded\">Sale</span>\n                  )}\n                  {product.isNew && (\n                    <span className=\"bg-green-500 text-white text-xs px-2 py-1 rounded\">New</span>\n                  )}\n                </div>\n              </div>\n\n              {/* Product Info */}\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{product.name}</h3>\n                \n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    {product.originalPrice && (\n                      <span className=\"text-gray-400 line-through text-sm\">\n                        {formatPrice(product.originalPrice)}\n                      </span>\n                    )}\n                    <span className=\"text-xl font-bold text-gray-900\">\n                      {formatPrice(product.price)}\n                    </span>\n                  </div>\n                </div>\n\n                <Button className=\"w-full\" variant=\"primary\">\n                  Add to cart\n                </Button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Load More Button */}\n        <div className=\"text-center\">\n          <Button variant=\"outline\" size=\"lg\">\n            Load More Products\n          </Button>\n        </div>\n      </Container>\n    </section>\n  );\n};\n\nexport default ProductShowcase;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,kBAA4B;;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,UAAU;QAAC;QAAO;QAAiB;QAAa;KAAQ;IAC9D,MAAM,cAAc;QAAC;QAAc;QAAkB;QAAW;QAAsB;KAAqB;IAE3G,MAAM,mBAAmB,0HAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,UACvC,iBAAiB,SAAS,QAAQ,QAAQ,KAAK;IAGjD,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC,wIAAA,CAAA,UAAS;;8BAER,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;sCAC/C,6LAAC;4BAAG,WAAU;;gCAA6D;8CAEzE,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;;;;;;;8BAKrC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,AAAC,gEAIX,OAHC,iBAAiB,SACb,4BACA;8CAGL;mCARI;;;;;;;;;;sCAcX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAoC;;;;;;8CACpD,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;8CAET,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4CAAoB,OAAO;sDACzB;2CADU;;;;;;;;;;;;;;;;;;;;;;8BASrB,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;4BAAqB,WAAU;;8CAE9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,IAAI;4CACjB,IAAI;4CACJ,WAAU;4CACV,eAAe,AAAC,GAAe,OAAb,QAAQ,IAAI,EAAC;4CAC/B,cAAa;;;;;;sDAIf,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,QAAQ,kBACf,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;gDAEnE,QAAQ,KAAK,kBACZ,6LAAC;oDAAK,WAAU;8DAAoD;;;;;;;;;;;;;;;;;;8CAM1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C,QAAQ,IAAI;;;;;;sDAEtE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,aAAa,kBACpB,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,aAAa;;;;;;kEAGtC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;;;;;;;;;;;;sDAKhC,6LAAC,qIAAA,CAAA,UAAM;4CAAC,WAAU;4CAAS,SAAQ;sDAAU;;;;;;;;;;;;;2BAxCvC,QAAQ,EAAE;;;;;;;;;;8BAiDxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;wBAAC,SAAQ;wBAAU,MAAK;kCAAK;;;;;;;;;;;;;;;;;;;;;;AAO9C;GAxHM;KAAA;uCA0HS", "debugId": null}}]}
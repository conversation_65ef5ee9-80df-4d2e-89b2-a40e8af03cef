import React from 'react'
import { Text, RichText, Repeater, types } from 'react-bricks/rsc'
import classNames from 'classnames'

interface ProductGridProps {
  backgroundColor: types.IColor
  columns: 2 | 3 | 4
  showFilters: boolean
  showSorting: boolean
}

const ProductGrid: types.Brick<ProductGridProps> = ({
  backgroundColor = { color: '#ffffff', className: 'bg-white' },
  columns = 3,
  showFilters = true,
  showSorting = true,
}) => {
  const gridClasses = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  }

  return (
    <section
      className={classNames('py-16', backgroundColor?.className)}
      style={{ backgroundColor: backgroundColor?.color }}
    >
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <Text
            propName="sectionTitle"
            placeholder="The Irresistible World of Floral Decorations"
            renderBlock={(props) => (
              <h2 className="text-4xl md:text-5xl font-light text-gray-900 mb-6">
                {props.children}
              </h2>
            )}
          />
          
          <RichText
            propName="sectionDescription"
            placeholder="Discover our carefully curated collection of fresh flowers, elegant arrangements, and seasonal bouquets."
            renderBlock={(props) => (
              <div className="text-lg text-gray-600 max-w-2xl mx-auto">
                {props.children}
              </div>
            )}
            allowedFeatures={[
              types.RichTextFeatures.Bold,
              types.RichTextFeatures.Italic,
            ]}
          />
        </div>

        {/* Filters and Sorting */}
        {(showFilters || showSorting) && (
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            {/* Category Filters */}
            {showFilters && (
              <div className="flex flex-wrap gap-2">
                <button className="px-4 py-2 bg-gray-900 text-white text-sm font-medium hover:bg-gray-800 transition-colors">
                  All
                </button>
                <button className="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium hover:bg-gray-200 transition-colors">
                  Anniversaries
                </button>
                <button className="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium hover:bg-gray-200 transition-colors">
                  Birthdays
                </button>
                <button className="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium hover:bg-gray-200 transition-colors">
                  Gifts
                </button>
                <button className="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium hover:bg-gray-200 transition-colors">
                  Weddings
                </button>
              </div>
            )}

            {/* Sorting */}
            {showSorting && (
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">Sort By:</span>
                <select className="border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500">
                  <option>Popularity</option>
                  <option>Price: Low to High</option>
                  <option>Price: High to Low</option>
                  <option>Newest</option>
                  <option>Name A-Z</option>
                </select>
              </div>
            )}
          </div>
        )}

        {/* Product Grid */}
        <div className={classNames('grid gap-8', gridClasses[columns])}>
          <Repeater
            propName="products"
            itemProps={{
              isOnSale: false,
              isNew: false,
              showPrice: true,
            }}
          />
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <Text
            propName="loadMoreText"
            placeholder="View More Products"
            renderBlock={(props) => (
              <button className="bg-gray-900 text-white px-8 py-3 font-medium hover:bg-gray-800 transition-colors duration-200">
                {props.children}
              </button>
            )}
          />
        </div>
      </div>
    </section>
  )
}

ProductGrid.schema = {
  name: 'product-grid',
  label: 'Product Grid',
  category: 'Florist',
  tags: ['products', 'grid', 'shop', 'florist'],
  previewImageUrl: '/api/preview/product-grid.png',
  
  getDefaultProps: () => ({
    backgroundColor: { color: '#ffffff', className: 'bg-white' },
    columns: 3,
    showFilters: true,
    showSorting: true,
    sectionTitle: 'The Irresistible World of Floral Decorations',
    sectionDescription: 'Discover our carefully curated collection of fresh flowers, elegant arrangements, and seasonal bouquets crafted with passion and expertise.',
    loadMoreText: 'View More Products',
    products: [
      {
        productName: 'White Roses',
        currentPrice: '$25',
        originalPrice: '$45',
        description: 'Elegant white roses perfect for any occasion',
        buttonText: 'Add to Cart',
        isOnSale: true,
        isNew: false,
        showPrice: true,
      },
      {
        productName: 'Spring Bouquet',
        currentPrice: '$45',
        description: 'Fresh spring flowers in vibrant colors',
        buttonText: 'Add to Cart',
        isOnSale: false,
        isNew: true,
        showPrice: true,
      },
      {
        productName: 'Garden Mix',
        currentPrice: '$35',
        description: 'Mixed seasonal flowers from our garden',
        buttonText: 'Add to Cart',
        isOnSale: false,
        isNew: false,
        showPrice: true,
      },
    ],
  }),

  sideEditProps: [
    {
      name: 'backgroundColor',
      label: 'Background Color',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Color,
        options: [
          { value: { color: '#ffffff', className: 'bg-white' }, label: 'White' },
          { value: { color: '#f8f9fa', className: 'bg-gray-50' }, label: 'Light Gray' },
          { value: { color: '#f0f8f0', className: 'bg-green-25' }, label: 'Light Green' },
        ],
      },
    },
    {
      name: 'columns',
      label: 'Grid Columns',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 2, label: '2 Columns' },
          { value: 3, label: '3 Columns' },
          { value: 4, label: '4 Columns' },
        ],
      },
    },
    {
      name: 'showFilters',
      label: 'Show Category Filters',
      type: types.SideEditPropType.Boolean,
    },
    {
      name: 'showSorting',
      label: 'Show Sorting Options',
      type: types.SideEditPropType.Boolean,
    },
  ],

  repeaterItems: [
    {
      name: 'products',
      itemType: 'product-card',
      itemLabel: 'Product',
      min: 1,
      max: 12,
    },
  ],
}

export default ProductGrid

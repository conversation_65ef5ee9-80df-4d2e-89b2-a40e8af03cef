{"name": "tomelia-gelees", "private": true, "license": "SEE LICENSE IN LICENSE", "version": "0.0.1-beta.0", "author": "", "scripts": {"dev": "next dev", "build": "next build -d", "start": "next start"}, "dependencies": {"classnames": "^2.5.1", "next": "15.2.2", "next-themes": "^0.4.6", "normalize.css": "^8.0.1", "react": "19.0.0", "react-bricks": "^4.7.1", "react-dom": "19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "19.0.10", "typescript": "^5"}, "prettier": {"printWidth": 80, "semi": false, "singleQuote": true, "trailingComma": "es5"}}
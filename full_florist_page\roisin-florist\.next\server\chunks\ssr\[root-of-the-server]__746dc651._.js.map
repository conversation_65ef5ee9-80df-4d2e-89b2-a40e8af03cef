{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b625b40b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_b625b40b-module__n-SICG__className\",\n  \"variable\": \"inter_b625b40b-module__n-SICG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b625b40b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/crimson_text_672493ea.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"crimson_text_672493ea-module__QiT-FG__className\",\n  \"variable\": \"crimson_text_672493ea-module__QiT-FG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/crimson_text_672493ea.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Crimson_Text%22,%22arguments%22:[{%22variable%22:%22--font-crimson%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22600%22,%22700%22],%22style%22:[%22normal%22,%22italic%22]}],%22variableName%22:%22crimsonText%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Crimson Text', 'Crimson Text Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,4JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,4JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,4JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return `$${price}`\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  return phone.replace(/\\s/g, '').replace(/(\\d{3})(\\d{1})(\\d{2})(\\d{4})/, '+$1 $2 $3 $4')\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAEO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,gCAAgC;AAC1E;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/ui/Container.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n}\n\nconst Container: React.FC<ContainerProps> = ({ \n  children, \n  className, \n  size = 'lg' \n}) => {\n  const sizes = {\n    sm: 'max-w-2xl',\n    md: 'max-w-4xl',\n    lg: 'max-w-6xl',\n    xl: 'max-w-7xl',\n    full: 'max-w-full',\n  };\n\n  return (\n    <div className={cn('mx-auto px-4 sm:px-6 lg:px-8', sizes[size], className)}>\n      {children}\n    </div>\n  );\n};\n\nexport default Container;\n"], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACZ;IACC,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC,KAAK,CAAC,KAAK,EAAE;kBAC7D;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/data/mockData.ts"], "sourcesContent": ["import { NavItem, Product, TeamMember, Service, ContactInfo, Feature } from '@/types';\n\nexport const navigationItems: NavItem[] = [\n  {\n    label: 'Home',\n    href: '/',\n    children: [\n      { label: 'Orchid', href: '/' },\n      { label: 'Daisy', href: '/daisy' },\n      { label: 'Gardenia', href: '/gardenia' },\n      { label: 'Dahlia', href: '/dahlia' },\n      { label: 'Bluebell', href: '/bluebell' },\n      { label: 'Primrose', href: '/primrose' },\n      { label: 'Landing', href: '/landing' },\n    ],\n  },\n  {\n    label: 'Pages',\n    href: '/pages',\n    children: [\n      { label: 'About Me', href: '/about-me' },\n      { label: 'About Us', href: '/about-us' },\n      { label: 'Contact Us', href: '/contact-us' },\n      { label: 'Get In Touch', href: '/get-in-touch' },\n      { label: 'Our Partners', href: '/our-partners' },\n      { label: 'Our Services', href: '/our-services' },\n      { label: 'Our Team', href: '/our-team' },\n      { label: 'FAQ Page', href: '/faq-page' },\n    ],\n  },\n  {\n    label: 'Portfolio',\n    href: '/portfolio',\n    children: [\n      { label: 'Standard', href: '/portfolio/standard' },\n      { label: 'Gallery', href: '/portfolio/gallery' },\n      { label: 'Gallery Joined', href: '/portfolio/gallery-joined' },\n    ],\n  },\n  {\n    label: 'Blog',\n    href: '/blog',\n    children: [\n      { label: 'Right Sidebar', href: '/blog/right-sidebar' },\n      { label: 'Left Sidebar', href: '/blog/left-sidebar' },\n      { label: 'No Sidebar', href: '/blog/no-sidebar' },\n      { label: 'Masonry Blog', href: '/blog/masonry' },\n    ],\n  },\n  {\n    label: 'Shop',\n    href: '/shop',\n    children: [\n      { label: 'Shop List', href: '/shop' },\n      { label: 'Cart', href: '/cart' },\n      { label: 'Checkout', href: '/checkout' },\n      { label: 'My Account', href: '/my-account' },\n    ],\n  },\n];\n\nexport const contactInfo: ContactInfo = {\n  address: '1087, Bathurst st Toronto ON',\n  phone: '+364 1 65 6365',\n  hours: 'Mon-Thu: 10:00-16:00 Sat-Sun: 10:00-16:00',\n};\n\nexport const products: Product[] = [\n  {\n    id: '1',\n    name: 'White',\n    price: 25,\n    originalPrice: 45,\n    image: '/placeholder-flower-1.jpg',\n    category: 'Anniversaries',\n    isOnSale: true,\n  },\n  {\n    id: '2',\n    name: 'Impatiens',\n    price: 45,\n    image: '/placeholder-flower-2.jpg',\n    category: 'Birthdays',\n    isNew: true,\n  },\n  {\n    id: '3',\n    name: 'Mazus',\n    price: 45,\n    image: '/placeholder-flower-3.jpg',\n    category: 'Gifts',\n  },\n  {\n    id: '4',\n    name: 'Pansies',\n    price: 45,\n    image: '/placeholder-flower-4.jpg',\n    category: 'Anniversaries',\n  },\n  {\n    id: '5',\n    name: 'Dahlia',\n    price: 45,\n    image: '/placeholder-flower-5.jpg',\n    category: 'Birthdays',\n  },\n  {\n    id: '6',\n    name: 'Peony',\n    price: 45,\n    image: '/placeholder-flower-6.jpg',\n    category: 'Gifts',\n  },\n];\n\nexport const teamMembers: TeamMember[] = [\n  {\n    id: '1',\n    name: 'Velva Kopf',\n    role: 'Biologist',\n    image: '/placeholder-team-1.jpg',\n  },\n  {\n    id: '2',\n    name: 'Elizabeth Morris',\n    role: 'Florist',\n    image: '/placeholder-team-2.jpg',\n  },\n  {\n    id: '3',\n    name: 'Blaine Bush',\n    role: 'Photographer',\n    image: '/placeholder-team-3.jpg',\n  },\n];\n\nexport const services: Service[] = [\n  {\n    id: '1',\n    title: 'New arrangements',\n    description: 'Fresh and creative floral arrangements',\n    icon: '/placeholder-icon-1.png',\n  },\n  {\n    id: '2',\n    title: 'Flower specialist',\n    description: 'Expert knowledge in flower care',\n    icon: '/placeholder-icon-2.png',\n  },\n];\n\nexport const features: Feature[] = [\n  {\n    id: '1',\n    title: 'Online Order',\n    description: 'Easy online ordering system',\n    icon: '/placeholder-feature-1.png',\n    link: '#',\n  },\n  {\n    id: '2',\n    title: 'Delivery in 2-4 h',\n    description: 'Fast delivery service',\n    icon: '/placeholder-feature-2.png',\n    link: '#',\n  },\n  {\n    id: '3',\n    title: 'Freshness',\n    description: 'Always fresh flowers',\n    icon: '/placeholder-feature-3.png',\n    link: '#',\n  },\n  {\n    id: '4',\n    title: 'Made by Artists',\n    description: 'Crafted by professional florists',\n    icon: '/placeholder-feature-4.png',\n    link: '#',\n  },\n];\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,kBAA6B;IACxC;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAU,MAAM;YAAI;YAC7B;gBAAE,OAAO;gBAAS,MAAM;YAAS;YACjC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAU,MAAM;YAAU;YACnC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAW,MAAM;YAAW;SACtC;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAc,MAAM;YAAc;YAC3C;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;YAC/C;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;YAC/C;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;YAC/C;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAY,MAAM;YAAY;SACxC;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAY,MAAM;YAAsB;YACjD;gBAAE,OAAO;gBAAW,MAAM;YAAqB;YAC/C;gBAAE,OAAO;gBAAkB,MAAM;YAA4B;SAC9D;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAiB,MAAM;YAAsB;YACtD;gBAAE,OAAO;gBAAgB,MAAM;YAAqB;YACpD;gBAAE,OAAO;gBAAc,MAAM;YAAmB;YAChD;gBAAE,OAAO;gBAAgB,MAAM;YAAgB;SAChD;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAa,MAAM;YAAQ;YACpC;gBAAE,OAAO;gBAAQ,MAAM;YAAQ;YAC/B;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAc,MAAM;YAAc;SAC5C;IACH;CACD;AAEM,MAAM,cAA2B;IACtC,SAAS;IACT,OAAO;IACP,OAAO;AACT;AAEO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;IACZ;CACD;AAEM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAEM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAEM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Container from '@/components/ui/Container';\nimport { contactInfo } from '@/data/mockData';\n\nconst Footer: React.FC = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <Container>\n        <div className=\"py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company Info */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-2xl font-bold text-green-400\">Roisin</h3>\n              <p className=\"text-gray-300 text-sm leading-relaxed\">\n                Creating beautiful floral arrangements that inspire and delight. \n                Your trusted florist for all occasions.\n              </p>\n              <div className=\"flex space-x-4\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-green-400 transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-green-400 transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-green-400 transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-green-400 transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n\n            {/* Quick Links */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold\">Quick Links</h4>\n              <ul className=\"space-y-2 text-sm\">\n                <li><Link href=\"/about\" className=\"text-gray-300 hover:text-green-400 transition-colors\">About Us</Link></li>\n                <li><Link href=\"/services\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Our Services</Link></li>\n                <li><Link href=\"/portfolio\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Portfolio</Link></li>\n                <li><Link href=\"/blog\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Blog</Link></li>\n                <li><Link href=\"/contact\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Contact</Link></li>\n              </ul>\n            </div>\n\n            {/* Services */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold\">Services</h4>\n              <ul className=\"space-y-2 text-sm\">\n                <li><Link href=\"/wedding-flowers\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Wedding Flowers</Link></li>\n                <li><Link href=\"/event-decoration\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Event Decoration</Link></li>\n                <li><Link href=\"/corporate-flowers\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Corporate Flowers</Link></li>\n                <li><Link href=\"/sympathy-flowers\" className=\"text-gray-300 hover:text-green-400 transition-colors\">Sympathy Flowers</Link></li>\n                <li><Link href=\"/house-plants\" className=\"text-gray-300 hover:text-green-400 transition-colors\">House Plants</Link></li>\n              </ul>\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-lg font-semibold\">Contact Info</h4>\n              <div className=\"space-y-3 text-sm\">\n                <div className=\"flex items-start space-x-2\">\n                  <svg className=\"w-4 h-4 mt-1 text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                  <span className=\"text-gray-300\">{contactInfo.address}</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <svg className=\"w-4 h-4 text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                  <span className=\"text-gray-300\">{contactInfo.phone}</span>\n                </div>\n                <div className=\"flex items-start space-x-2\">\n                  <svg className=\"w-4 h-4 mt-1 text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <span className=\"text-gray-300\">{contactInfo.hours}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 Roisin Florist. All rights reserved.\n            </p>\n            <div className=\"flex space-x-6 text-sm\">\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-green-400 transition-colors\">\n                Privacy Policy\n              </Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-green-400 transition-colors\">\n                Terms of Service\n              </Link>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,MAAM,SAAmB;IACvB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC,qIAAA,CAAA,UAAS;;8BACR,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAIrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAuD;;;;;;;;;;;0DACzF,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAuD;;;;;;;;;;;0DAC5F,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;8DAAuD;;;;;;;;;;;0DAC7F,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;8DAAuD;;;;;;;;;;;0DACxF,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAuD;;;;;;;;;;;;;;;;;;;;;;;0CAK/F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,WAAU;8DAAuD;;;;;;;;;;;0DACnG,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAoB,WAAU;8DAAuD;;;;;;;;;;;0DACpG,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;8DAAuD;;;;;;;;;;;0DACrG,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAoB,WAAU;8DAAuD;;;;;;;;;;;0DACpG,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,WAAU;8DAAuD;;;;;;;;;;;;;;;;;;;;;;;0CAKpG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EACrF,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;kEAEvE,8OAAC;wDAAK,WAAU;kEAAiB,uHAAA,CAAA,cAAW,CAAC,OAAO;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAChF,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,8OAAC;wDAAK,WAAU;kEAAiB,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;;;;;;0DAEpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACrF,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,8OAAC;wDAAK,WAAU;kEAAiB,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ5D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAuD;;;;;;kDAGvF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnG;uCAEe", "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Stalinis%20kompiuteris/React-Bricks/full_florist_page/roisin-florist/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Inter, Crimson_Text } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n});\n\nconst crimsonText = Crimson_Text({\n  variable: \"--font-crimson\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"600\", \"700\"],\n  style: [\"normal\", \"italic\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Roisin - Beautiful Florist & Plant Lover\",\n  description: \"We produce beauty inspired by flora. Professional florist services in Ottawa with fresh flowers, arrangements, and plant care.\",\n  keywords: \"florist, flowers, bouquet, plants, Ottawa, wedding flowers, arrangements\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${inter.variable} ${crimsonText.variable} font-sans antialiased`}\n      >\n        <Header />\n        {children}\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,gJAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,sBAAsB,CAAC;;8BAE5E,8OAAC,sIAAA,CAAA,UAAM;;;;;gBACN;8BACD,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}
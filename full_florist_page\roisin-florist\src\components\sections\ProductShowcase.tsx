'use client';

import React, { useState } from 'react';
import Image from '@/components/ui/Image';
import Container from '@/components/ui/Container';
import Button from '@/components/ui/Button';
import { products } from '@/data/mockData';
import { formatPrice } from '@/lib/utils';

const ProductShowcase: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('All');
  const [sortBy, setSortBy] = useState('Popularity');

  const filters = ['All', 'Anniversaries', 'Birthdays', 'Gifts'];
  const sortOptions = ['Popularity', 'Average rating', 'Newness', 'Price: Low to High', 'Price: High to Low'];

  const filteredProducts = products.filter(product => 
    activeFilter === 'All' || product.category === activeFilter
  );

  return (
    <section className="py-20 bg-gray-50">
      <Container>
        {/* Section Header */}
        <div className="text-center mb-12">
          <p className="text-green-700 font-medium mb-2"><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON></p>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
            The Irresistible World of
            <br />
            <span className="text-green-700">Floral Decorations</span>
          </h2>
        </div>

        {/* Filters and Sort */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <button
                key={filter}
                onClick={() => setActiveFilter(filter)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  activeFilter === filter
                    ? 'bg-green-700 text-white'
                    : 'bg-white text-gray-700 hover:bg-green-50 hover:text-green-700'
                }`}
              >
                {filter}
              </button>
            ))}
          </div>

          {/* Sort Dropdown */}
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Sort By</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              {sortOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {filteredProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow-sm overflow-hidden group hover:shadow-lg transition-shadow">
              {/* Product Image */}
              <div className="relative h-64 overflow-hidden">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  unsplashQuery={`${product.name},flower,bouquet`}
                  unsplashSize="400x400"
                />
                
                {/* Badges */}
                <div className="absolute top-3 left-3 space-y-1">
                  {product.isOnSale && (
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded">Sale</span>
                  )}
                  {product.isNew && (
                    <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">New</span>
                  )}
                </div>
              </div>

              {/* Product Info */}
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
                
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    {product.originalPrice && (
                      <span className="text-gray-400 line-through text-sm">
                        {formatPrice(product.originalPrice)}
                      </span>
                    )}
                    <span className="text-xl font-bold text-gray-900">
                      {formatPrice(product.price)}
                    </span>
                  </div>
                </div>

                <Button className="w-full" variant="primary">
                  Add to cart
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center">
          <Button variant="outline" size="lg">
            Load More Products
          </Button>
        </div>
      </Container>
    </section>
  );
};

export default ProductShowcase;

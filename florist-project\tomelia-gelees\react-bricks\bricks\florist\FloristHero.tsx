import classNames from 'classnames'
import React from 'react'
import { Repeater, RichText, types, Text } from 'react-bricks/rsc'
import {
  textGradientEditProps,
  highlightTextEditProps,
  sectionDefaults,
  LayoutProps,
  backgroundWithImageBgSideGroup,
  paddingBordersSideGroup,
} from '../shared/LayoutSideProps'
import {
  buttonColors,
  gradients,
  highlightTextColors,
  textColors,
} from '../shared/colors'
import Container from '../shared/components/Container'
import Section from '../shared/components/Section'

export interface FloristHeroProps extends LayoutProps {
  size: 'medium' | 'large'
  textGradient: keyof typeof gradients
  highlightTextColor: { color: string; className: string }
  title: types.TextValue
  subtitle: types.TextValue
  description: types.TextValue
  buttons: types.RepeaterItems
}

const FloristHero: types.Brick<FloristHeroProps> = ({
  backgroundColor,
  backgroundImage,
  borderTop,
  borderBottom,
  paddingTop,
  paddingBottom,
  size = 'large',
  textGradient = gradients.NONE.value,
  highlightTextColor = highlightTextColors.PINK.value,
  title,
  subtitle,
  description,
  buttons,
}: FloristHeroProps) => {
  const titleColor = textColors.GRAY_800
  const textColor = textColors.GRAY_700
  const titleStyle =
    textGradient !== gradients.NONE.value
      ? { WebkitTextFillColor: 'transparent' }
      : {}

  return (
    <Section
      backgroundColor={backgroundColor}
      backgroundImage={backgroundImage}
      borderTop={borderTop}
      borderBottom={borderBottom}
    >
      <Container paddingTop={paddingTop} paddingBottom={paddingBottom}>
        <div className="max-w-4xl mx-auto px-5">
          {/* Subtitle */}
          <Text
            propName="subtitle"
            renderBlock={(props) => (
              <p
                className="text-lg font-medium text-pink-600 mb-4 text-center"
                {...props.attributes}
              >
                {props.children}
              </p>
            )}
            placeholder="We Create Beauty Inspired by Flora"
          />

          {/* Title */}
          <div className={titleColor} style={titleStyle}>
            <RichText
              propName="title"
              value={title}
              renderBlock={(props) => (
                <h1
                  className={classNames(
                    'text-[32px] leading-9 sm:text-[48px] sm:leading-tight text-center font-extrabold mb-6 pb-1 bg-clip-text bg-linear-to-r',
                    { 'lg:text-6xl lg:leading-tight': size === 'large' },
                    titleColor,
                    gradients[textGradient]?.className
                  )}
                  {...props.attributes}
                >
                  {props.children}
                </h1>
              )}
              allowedFeatures={[types.RichTextFeatures.Highlight]}
              placeholder="Beautiful Floral Arrangements"
              renderHighlight={({ children }) => (
                <span className={highlightTextColor.className}>{children}</span>
              )}
            />
          </div>

          {/* Description */}
          <RichText
            propName="description"
            value={description}
            renderBlock={(props) => (
              <p
                className={classNames(
                  'text-lg leading-7 sm:text-xl sm:leading-8 text-center mb-8',
                  textColor
                )}
                {...props.attributes}
              >
                {props.children}
              </p>
            )}
            placeholder="Discover our exquisite collection of fresh flowers, elegant arrangements, and botanical artistry..."
            allowedFeatures={[
              types.RichTextFeatures.Bold,
              types.RichTextFeatures.Link,
            ]}
            renderLink={(props) => (
              <a
                href={props.href}
                target={props.target}
                rel={props.rel}
                className="text-pink-500 hover:text-pink-600 transition-colors"
              >
                {props.children}
              </a>
            )}
          />

          {/* Buttons */}
          <Repeater
            propName="buttons"
            items={buttons}
            renderWrapper={(items) => (
              <div className="flex flex-row space-x-5 items-center justify-center">
                {items}
              </div>
            )}
          />
        </div>
      </Container>
    </Section>
  )
}

FloristHero.schema = {
  name: 'florist-hero',
  label: 'Florist Hero Section',
  category: 'Florist',
  tags: ['hero', 'florist', 'banner'],
  previewImageUrl: '/api/preview/florist-hero.png',

  getDefaultProps: () => ({
    ...sectionDefaults,
    size: 'large',
    paddingTop: '16',
    paddingBottom: '16',
    textGradient: gradients.NONE.value,
    highlightTextColor: highlightTextColors.PINK.value,
    title: [
      {
        type: 'paragraph',
        children: [
          {
            text: 'Beautiful ',
          },
          {
            text: 'Floral',
            highlight: true,
          },
          {
            text: ' Arrangements',
          },
        ],
      },
    ],
    subtitle: 'Tomelia - We Create Beauty Inspired by Flora',
    description: 'Discover our exquisite collection of fresh flowers, elegant arrangements, and botanical artistry. Each creation tells a story of natural beauty and craftsmanship.',
    buttons: [
      {
        type: 'link',
        text: 'Shop Arrangements',
        href: '/products',
        isTargetBlank: false,
        buttonType: 'submit',
        variant: 'solid',
        padding: 'normal',
        simpleAnchorLink: false,
      },
      {
        type: 'link',
        text: 'Contact Us',
        href: '/contact',
        isTargetBlank: false,
        buttonType: 'submit',
        variant: 'outline',
        padding: 'normal',
        simpleAnchorLink: false,
      },
    ],
  }),

  repeaterItems: [
    {
      name: 'buttons',
      itemType: 'button',
      itemLabel: 'Button',
      min: 0,
      max: 2,
    },
  ],

  sideEditProps: [
    {
      groupName: 'Title',
      defaultOpen: true,
      props: [
        textGradientEditProps,
        {
          name: 'size',
          label: 'Title size',
          type: types.SideEditPropType.Select,
          selectOptions: {
            display: types.OptionsDisplay.Radio,
            options: [
              { value: 'medium', label: 'Medium' },
              { value: 'large', label: 'Large' },
            ],
          },
        },
        highlightTextEditProps,
      ],
    },
    backgroundWithImageBgSideGroup,
    paddingBordersSideGroup,
  ],
}

export default FloristHero

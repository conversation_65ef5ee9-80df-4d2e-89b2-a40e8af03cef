globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[lang]/[[...slug]]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/ReactBricksApp.tsx":{"*":{"id":"(ssr)/./components/ReactBricksApp.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/themeProvider.tsx":{"*":{"id":"(ssr)/./components/themeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/HeroUnit.module.css":{"*":{"id":"(ssr)/./css/HeroUnit.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Features.module.css":{"*":{"id":"(ssr)/./css/Features.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/FeatureItem.module.css":{"*":{"id":"(ssr)/./css/FeatureItem.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Button.module.css":{"*":{"id":"(ssr)/./css/Button.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Footer.module.css":{"*":{"id":"(ssr)/./css/Footer.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/FooterColumn.module.css":{"*":{"id":"(ssr)/./css/FooterColumn.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/FooterLink.module.css":{"*":{"id":"(ssr)/./css/FooterLink.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Header.module.css":{"*":{"id":"(ssr)/./css/Header.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/HeaderMenuItem.module.css":{"*":{"id":"(ssr)/./css/HeaderMenuItem.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/HeaderMenuSubItem.module.css":{"*":{"id":"(ssr)/./css/HeaderMenuSubItem.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderClient.tsx":{"*":{"id":"(ssr)/./react-bricks/bricks/layout/HeaderClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderMenuItemClient.tsx":{"*":{"id":"(ssr)/./react-bricks/bricks/layout/HeaderMenuItemClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderMenuItemProvider.tsx":{"*":{"id":"(ssr)/./react-bricks/bricks/layout/HeaderMenuItemProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderProvider.tsx":{"*":{"id":"(ssr)/./react-bricks/bricks/layout/HeaderProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/ItemMenuClient.tsx":{"*":{"id":"(ssr)/./react-bricks/bricks/layout/ItemMenuClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/NextLinkClient.tsx":{"*":{"id":"(ssr)/./react-bricks/NextLinkClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-bricks/rsc/client/react-bricks-rsc-client.esm.js":{"*":{"id":"(ssr)/./node_modules/react-bricks/rsc/client/react-bricks-rsc-client.esm.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/ReactBricksApp.tsx":{"*":{"id":"(ssr)/./app/admin/ReactBricksApp.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(ssr)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/editor/page.tsx":{"*":{"id":"(ssr)/./app/admin/editor/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\components\\ReactBricksApp.tsx":{"id":"(app-pages-browser)/./components/ReactBricksApp.tsx","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\components\\themeProvider.tsx":{"id":"(app-pages-browser)/./components/themeProvider.tsx","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\errorNoFooter.module.css":{"id":"(app-pages-browser)/./css/errorNoFooter.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\errorNoHeader.module.css":{"id":"(app-pages-browser)/./css/errorNoHeader.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\errorNoKeys.module.css":{"id":"(app-pages-browser)/./css/errorNoKeys.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\layout.module.css":{"id":"(app-pages-browser)/./css/layout.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\HeroUnit.module.css":{"id":"(app-pages-browser)/./css/HeroUnit.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\Features.module.css":{"id":"(app-pages-browser)/./css/Features.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\FeatureItem.module.css":{"id":"(app-pages-browser)/./css/FeatureItem.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\Button.module.css":{"id":"(app-pages-browser)/./css/Button.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\Footer.module.css":{"id":"(app-pages-browser)/./css/Footer.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\FooterColumn.module.css":{"id":"(app-pages-browser)/./css/FooterColumn.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\FooterLink.module.css":{"id":"(app-pages-browser)/./css/FooterLink.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\Header.module.css":{"id":"(app-pages-browser)/./css/Header.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\HeaderMenuItem.module.css":{"id":"(app-pages-browser)/./css/HeaderMenuItem.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\HeaderMenuSubItem.module.css":{"id":"(app-pages-browser)/./css/HeaderMenuSubItem.module.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\[lang]\\\\layout.tsx\",\"import\":\"Nunito_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"weight\":[\"300\",\"400\",\"600\",\"700\",\"800\",\"900\"],\"style\":[\"normal\",\"italic\"],\"variable\":\"--font-nunito\"}],\"variableName\":\"nunito\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\[lang]\\\\layout.tsx\",\"import\":\"Nunito_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"weight\":[\"300\",\"400\",\"600\",\"700\",\"800\",\"900\"],\"style\":[\"normal\",\"italic\"],\"variable\":\"--font-nunito\"}],\"variableName\":\"nunito\"}","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\css\\style.css":{"id":"(app-pages-browser)/./css/style.css","name":"*","chunks":["app/[lang]/layout","static/chunks/app/%5Blang%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\react-bricks\\bricks\\layout\\HeaderClient.tsx":{"id":"(app-pages-browser)/./react-bricks/bricks/layout/HeaderClient.tsx","name":"*","chunks":["app/[lang]/[[...slug]]/page","static/chunks/app/%5Blang%5D/%5B%5B...slug%5D%5D/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\react-bricks\\bricks\\layout\\HeaderMenuItemClient.tsx":{"id":"(app-pages-browser)/./react-bricks/bricks/layout/HeaderMenuItemClient.tsx","name":"*","chunks":["app/[lang]/[[...slug]]/page","static/chunks/app/%5Blang%5D/%5B%5B...slug%5D%5D/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\react-bricks\\bricks\\layout\\HeaderMenuItemProvider.tsx":{"id":"(app-pages-browser)/./react-bricks/bricks/layout/HeaderMenuItemProvider.tsx","name":"*","chunks":["app/[lang]/[[...slug]]/page","static/chunks/app/%5Blang%5D/%5B%5B...slug%5D%5D/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\react-bricks\\bricks\\layout\\HeaderProvider.tsx":{"id":"(app-pages-browser)/./react-bricks/bricks/layout/HeaderProvider.tsx","name":"*","chunks":["app/[lang]/[[...slug]]/page","static/chunks/app/%5Blang%5D/%5B%5B...slug%5D%5D/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\react-bricks\\bricks\\layout\\ItemMenuClient.tsx":{"id":"(app-pages-browser)/./react-bricks/bricks/layout/ItemMenuClient.tsx","name":"*","chunks":["app/[lang]/[[...slug]]/page","static/chunks/app/%5Blang%5D/%5B%5B...slug%5D%5D/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\react-bricks\\NextLinkClient.tsx":{"id":"(app-pages-browser)/./react-bricks/NextLinkClient.tsx","name":"*","chunks":["app/[lang]/[[...slug]]/page","static/chunks/app/%5Blang%5D/%5B%5B...slug%5D%5D/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\react-bricks\\rsc\\client\\react-bricks-rsc-client.esm.js":{"id":"(app-pages-browser)/./node_modules/react-bricks/rsc/client/react-bricks-rsc-client.esm.js","name":"*","chunks":["app/[lang]/[[...slug]]/page","static/chunks/app/%5Blang%5D/%5B%5B...slug%5D%5D/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\app\\admin\\ReactBricksApp.tsx":{"id":"(app-pages-browser)/./app/admin/ReactBricksApp.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./app/admin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\app\\admin\\editor\\page.tsx":{"id":"(app-pages-browser)/./app/admin/editor/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\":[],"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\app\\[lang]\\layout":[{"inlined":false,"path":"static/css/app/[lang]/layout.css"}],"C:\\Users\\<USER>\\OneDrive\\Stalinis kompiuteris\\React-Bricks\\florist-project\\tomelia-gelees\\app\\[lang]\\[[...slug]]\\page":[{"inlined":false,"path":"static/css/app/[lang]/[[...slug]]/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./components/ReactBricksApp.tsx":{"*":{"id":"(rsc)/./components/ReactBricksApp.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/themeProvider.tsx":{"*":{"id":"(rsc)/./components/themeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/errorNoFooter.module.css":{"*":{"id":"(rsc)/./css/errorNoFooter.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/errorNoHeader.module.css":{"*":{"id":"(rsc)/./css/errorNoHeader.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/errorNoKeys.module.css":{"*":{"id":"(rsc)/./css/errorNoKeys.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/layout.module.css":{"*":{"id":"(rsc)/./css/layout.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/HeroUnit.module.css":{"*":{"id":"(rsc)/./css/HeroUnit.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Features.module.css":{"*":{"id":"(rsc)/./css/Features.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/FeatureItem.module.css":{"*":{"id":"(rsc)/./css/FeatureItem.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Button.module.css":{"*":{"id":"(rsc)/./css/Button.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Footer.module.css":{"*":{"id":"(rsc)/./css/Footer.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/FooterColumn.module.css":{"*":{"id":"(rsc)/./css/FooterColumn.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/FooterLink.module.css":{"*":{"id":"(rsc)/./css/FooterLink.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/Header.module.css":{"*":{"id":"(rsc)/./css/Header.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/HeaderMenuItem.module.css":{"*":{"id":"(rsc)/./css/HeaderMenuItem.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/HeaderMenuSubItem.module.css":{"*":{"id":"(rsc)/./css/HeaderMenuSubItem.module.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./css/style.css":{"*":{"id":"(rsc)/./css/style.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderClient.tsx":{"*":{"id":"(rsc)/./react-bricks/bricks/layout/HeaderClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderMenuItemClient.tsx":{"*":{"id":"(rsc)/./react-bricks/bricks/layout/HeaderMenuItemClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderMenuItemProvider.tsx":{"*":{"id":"(rsc)/./react-bricks/bricks/layout/HeaderMenuItemProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/HeaderProvider.tsx":{"*":{"id":"(rsc)/./react-bricks/bricks/layout/HeaderProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/bricks/layout/ItemMenuClient.tsx":{"*":{"id":"(rsc)/./react-bricks/bricks/layout/ItemMenuClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./react-bricks/NextLinkClient.tsx":{"*":{"id":"(rsc)/./react-bricks/NextLinkClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-bricks/rsc/client/react-bricks-rsc-client.esm.js":{"*":{"id":"(rsc)/./node_modules/react-bricks/rsc/client/react-bricks-rsc-client.esm.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/ReactBricksApp.tsx":{"*":{"id":"(rsc)/./app/admin/ReactBricksApp.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(rsc)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/editor/page.tsx":{"*":{"id":"(rsc)/./app/admin/editor/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}
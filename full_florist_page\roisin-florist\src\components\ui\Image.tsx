import React from 'react';
import NextImage from 'next/image';
import { cn } from '@/lib/utils';

interface ImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  priority?: boolean;
  sizes?: string;
  unsplashQuery?: string;
  unsplashSize?: string;
}

const Image: React.FC<ImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  fill = false,
  priority = false,
  sizes,
  unsplashQuery,
  unsplashSize = '800x600',
  ...props
}) => {
  // Generate Unsplash URL if query is provided
  const getImageSrc = () => {
    if (unsplashQuery) {
      return `https://source.unsplash.com/${unsplashSize}/?${unsplashQuery}`;
    }
    return src;
  };

  const imageProps = {
    src: getImageSrc(),
    alt,
    className: cn('object-cover', className),
    priority,
    sizes,
    ...props,
  };

  if (fill) {
    return <NextImage {...imageProps} fill />;
  }

  return (
    <NextImage
      {...imageProps}
      width={width || 800}
      height={height || 600}
    />
  );
};

export default Image;

/* [next]/internal/font/google/crimson_text_672493ea.module.css [app-client] (css) */
@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("../media/ff573ea94913a577-s.006f222f.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("../media/83ffc44022e59d21-s.d4567eaf.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("../media/9ae8c32cd7923d35-s.p.b83c8185.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("../media/4fe43faadd3a148b-s.fc6ebf5b.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("../media/673b22802a1993ac-s.d338f5e7.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url("../media/615b54cb9a65ea6c-s.p.a0a5c9cd.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("../media/72f4963055aff60c-s.52c1356b.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("../media/f41265d93bd39ac5-s.03d0e51e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Crimson Text;
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url("../media/2d4bef04ea6560a5-s.p.d74a958a.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/7012b7d7e3aad293-s.d10213c2.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/5c0f7ad1594795bf-s.456ffa3b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/f306720e345d6c44-s.p.50c9d529.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/cd155c4cc63c827d-s.03644cb1.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/dc69c4b3cb753c1e-s.243ec6ee.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/f1039e6ccbc89d06-s.p.75e441a8.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/0602ac41c7a90ba7-s.49a15285.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/0272e86f63025b74-s.e28538e1.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Crimson Text;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/72bc28f85ea123a5-s.p.8c75f6b9.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Crimson Text Fallback;
  src: local(Times New Roman);
  ascent-override: 97.5%;
  descent-override: 36.01%;
  line-gap-override: 0.0%;
  size-adjust: 97.36%;
}

.crimson_text_672493ea-module__QiT-FG__className {
  font-family: Crimson Text, Crimson Text Fallback;
}

.crimson_text_672493ea-module__QiT-FG__variable {
  --font-crimson: "Crimson Text", "Crimson Text Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_crimson_text_672493ea_module_css_e59ae46c._.single.css.map*/
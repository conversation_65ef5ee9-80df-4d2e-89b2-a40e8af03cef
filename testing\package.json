{"name": "testing", "private": true, "license": "SEE LICENSE IN LICENSE", "version": "2.0.0", "author": "", "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"classnames": "^2.5.1", "dayjs": "^1.11.13", "email-validator": "^2.0.4", "jsonp": "^0.2.1", "next": "^15.2.2", "next-themes": "^0.4.6", "pigeon-maps": "^0.22.1", "prism-theme-night-owl": "^1.4.0", "prismjs": "^1.30.0", "react": "^19.0.0", "react-bricks": "^4.7.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-slick": "^0.30.3", "react-tweet": "^3.2.2", "slick-carousel": "^1.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.14", "@types/jsonp": "^0.2.3", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4", "@types/react-slick": "^0.23.13", "eslint": "^9.22.0", "eslint-config-next": "15.2.2", "postcss": "^8.5.3", "tailwindcss": "^4.0.14", "typescript": "^5"}, "prettier": {"printWidth": 80, "semi": false, "singleQuote": true, "trailingComma": "es5"}}
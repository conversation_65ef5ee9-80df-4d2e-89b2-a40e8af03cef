import React from 'react'
import { Text, RichText, Repeater, types } from 'react-bricks/rsc'
import classNames from 'classnames'

interface ServicesHighlightProps {
  backgroundColor: types.IColor
  layout: 'horizontal' | 'grid'
  showIcons: boolean
}

const ServicesHighlight: types.Brick<ServicesHighlightProps> = ({
  backgroundColor = { color: '#f8f9fa', className: 'bg-gray-50' },
  layout = 'horizontal',
  showIcons = true,
}) => {
  return (
    <section
      className={classNames('py-16', backgroundColor?.className)}
      style={{ backgroundColor: backgroundColor?.color }}
    >
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <Text
            propName="sectionTitle"
            placeholder="Why Choose Tomelia"
            renderBlock={(props) => (
              <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
                {props.children}
              </h2>
            )}
          />
          
          <RichText
            propName="sectionDescription"
            placeholder="We're committed to providing the finest floral experiences with exceptional service and quality."
            renderBlock={(props) => (
              <div className="text-lg text-gray-600 max-w-2xl mx-auto">
                {props.children}
              </div>
            )}
            allowedFeatures={[
              types.RichTextFeatures.Bold,
              types.RichTextFeatures.Italic,
            ]}
          />
        </div>

        {/* Services Grid */}
        <div className={classNames(
          layout === 'horizontal' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'
            : 'grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto'
        )}>
          <Repeater
            propName="services"
            itemProps={{
              showIcon: showIcons,
            }}
          />
        </div>
      </div>
    </section>
  )
}

// Service Item Component
interface ServiceItemProps {
  showIcon: boolean
}

const ServiceItem: types.Brick<ServiceItemProps> = ({ showIcon = true }) => {
  return (
    <div className="text-center group">
      {/* Icon */}
      {showIcon && (
        <div className="mb-6">
          <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
            <Text
              propName="iconEmoji"
              placeholder="🌸"
              renderBlock={(props) => (
                <span className="text-2xl">
                  {props.children}
                </span>
              )}
            />
          </div>
        </div>
      )}

      {/* Title */}
      <Text
        propName="serviceTitle"
        placeholder="Online Order"
        renderBlock={(props) => (
          <h3 className="text-xl font-medium text-gray-900 mb-3">
            {props.children}
          </h3>
        )}
      />

      {/* Description */}
      <RichText
        propName="serviceDescription"
        placeholder="Easy online ordering with secure payment and instant confirmation."
        renderBlock={(props) => (
          <div className="text-gray-600 leading-relaxed">
            {props.children}
          </div>
        )}
        allowedFeatures={[
          types.RichTextFeatures.Bold,
          types.RichTextFeatures.Italic,
        ]}
      />

      {/* Link */}
      <Text
        propName="linkText"
        placeholder="More info"
        renderBlock={(props) => (
          <a
            href="#"
            className="inline-block mt-4 text-green-600 hover:text-green-700 font-medium text-sm transition-colors duration-200"
          >
            {props.children}
          </a>
        )}
      />
    </div>
  )
}

ServiceItem.schema = {
  name: 'service-item',
  label: 'Service Item',
  category: 'Florist',
  tags: ['service', 'feature', 'highlight'],
  
  getDefaultProps: () => ({
    showIcon: true,
    iconEmoji: '🌸',
    serviceTitle: 'Online Order',
    serviceDescription: 'Easy online ordering with secure payment and instant confirmation.',
    linkText: 'More info',
  }),

  sideEditProps: [
    {
      name: 'showIcon',
      label: 'Show Icon',
      type: types.SideEditPropType.Boolean,
    },
  ],
}

ServicesHighlight.schema = {
  name: 'services-highlight',
  label: 'Services Highlight',
  category: 'Florist',
  tags: ['services', 'features', 'highlights', 'florist'],
  previewImageUrl: '/api/preview/services-highlight.png',
  
  getDefaultProps: () => ({
    backgroundColor: { color: '#f8f9fa', className: 'bg-gray-50' },
    layout: 'horizontal',
    showIcons: true,
    sectionTitle: 'Why Choose Tomelia',
    sectionDescription: 'We\'re committed to providing the finest floral experiences with exceptional service and quality.',
    services: [
      {
        iconEmoji: '💻',
        serviceTitle: 'Online Order',
        serviceDescription: 'Easy online ordering with secure payment and instant confirmation.',
        linkText: 'More info',
        showIcon: true,
      },
      {
        iconEmoji: '🚚',
        serviceTitle: 'Delivery in 2-4h',
        serviceDescription: 'Fast and reliable delivery to your doorstep within hours.',
        linkText: 'More info',
        showIcon: true,
      },
      {
        iconEmoji: '🌿',
        serviceTitle: 'Freshness',
        serviceDescription: 'Only the freshest flowers sourced daily from trusted growers.',
        linkText: 'More info',
        showIcon: true,
      },
      {
        iconEmoji: '🎨',
        serviceTitle: 'Made by Artists',
        serviceDescription: 'Each arrangement is crafted by our skilled floral artists.',
        linkText: 'More info',
        showIcon: true,
      },
    ],
  }),

  sideEditProps: [
    {
      name: 'backgroundColor',
      label: 'Background Color',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Color,
        options: [
          { value: { color: '#ffffff', className: 'bg-white' }, label: 'White' },
          { value: { color: '#f8f9fa', className: 'bg-gray-50' }, label: 'Light Gray' },
          { value: { color: '#f0f8f0', className: 'bg-green-25' }, label: 'Light Green' },
        ],
      },
    },
    {
      name: 'layout',
      label: 'Layout Style',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 'horizontal', label: 'Horizontal (4 columns)' },
          { value: 'grid', label: 'Grid (2 columns)' },
        ],
      },
    },
    {
      name: 'showIcons',
      label: 'Show Icons',
      type: types.SideEditPropType.Boolean,
    },
  ],

  repeaterItems: [
    {
      name: 'services',
      itemType: 'service-item',
      itemLabel: 'Service',
      min: 1,
      max: 8,
    },
  ],
}

export { ServiceItem }
export default ServicesHighlight

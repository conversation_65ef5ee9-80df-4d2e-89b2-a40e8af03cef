enum blockNames {
  FeatureItem = 'feature-item',
  Features = 'features',
  Cards = 'cards',
  Card = 'card',
  LinkCards = 'link-cards',
  LinkCard = 'link-card',
  HeroUnit = 'hero-unit',
  HeroUnit2 = 'hero-unit-2',
  Separator = 'separator',
  Testimonial = 'testimonial',
  Testimonial3Cols = 'testimonial-3-cols',
  Testimonial3ColsItem = 'testimonial-3-cols-item',
  Title = 'title',
  TextMedia = 'text-image', // TODO
  NewsletterHero = 'newsletter-hero',
  NewsletterSubscribe = 'newsletter-subscribe',
  Contacts = 'contacts',
  ContactItem = 'contact-item',
  Map = 'map',
  LogoGrid = 'logo-grid',
  LogoGridItem = 'logo-grid-item',
  SmallLogoGrid = 'small-logo-grid',
  SmallLogoGridItem = 'small-logo-grid-item',
  Team = 'team',
  TeamItem = 'team-item',
  Team2Cols = 'team-2-cols',
  Team2ColsItem = 'team-item2-cols-item',
  ImageBackground = 'image-background',
  Badge = 'badge',
  Button = 'button',
  BulletListItem = 'bullet-list-item',
  TextMediaLogo = 'text-image-logo', // TODO
  Customers = 'customers',
  Customer = 'customer',
  Faqs = 'faqs',
  Faq = 'faq',
  Faqs2cols = 'faqs-2-cols',
  CallToAction = 'call-to-action',
  FeatureCallout = 'big-feature',
  Documents = 'documents',
  Document = 'document',
  ImageCarousel = 'image-carousel',
  ImageCarouselItem = 'image-carousel-item',
  HorizontalRule = 'horizontal-rule',
  Spacer = 'spacer',
  FormSection = 'form-section',
  FormBuilder = 'form-builder',
  FormButton = 'form-button',
  FormCheckbox = 'form-checkbox',
  FormInput = 'form-input',
  FormRadiobuttons = 'form-radiobuttons',
  FormSelect = 'form-select',
  FormSingleRadio = 'form-single-radio',
  FormTextArea = 'form-text-area',
  Table = 'table',
  TableRow = 'table-row',
  TableCell = 'table-cell',
  Header = 'header',
  HeaderMenuItem = 'header-menu-item',
  HeaderMenuSubItem = 'header-menu-sub-item',
  Footer = 'footer',
  FooterColumn = 'footer-column',
  FooterLink = 'footer-link',
  Video = 'video',
  Code = 'code',
  Paragraph = 'paragraph',
  BigImage = 'big-image',
  Tweet = 'tweet',
  TweetLight = 'tweet-light',
  Pricing = 'pricing',
  PricingPlan = 'pricing-plan',
  PlanFeature = 'pricing-plan-feature',
  Offices = 'contact-offices',
  Office = 'contact-office',
  ContactsForm = 'contact-form',
  BlogTitle = 'blog-title',
}

export default blockNames

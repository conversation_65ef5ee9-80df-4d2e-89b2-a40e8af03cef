import React from 'react'
import { Text, Image, types } from 'react-bricks/rsc'
import classNames from 'classnames'

interface ProductCardProps {
  isOnSale: boolean
  isNew: boolean
  showPrice: boolean
}

const ProductCard: types.Brick<ProductCardProps> = ({
  isOnSale = false,
  isNew = false,
  showPrice = true,
}) => {
  return (
    <div className="group relative bg-white rounded-none overflow-hidden shadow-sm hover:shadow-lg transition-shadow duration-300">
      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden">
        <Image
          propName="productImage"
          alt="Product"
          imageClassName="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
        />
        
        {/* Badges */}
        <div className="absolute top-4 left-4 flex flex-col gap-2">
          {isOnSale && (
            <span className="bg-red-500 text-white px-3 py-1 text-sm font-medium">
              Sale
            </span>
          )}
          {isNew && (
            <span className="bg-green-600 text-white px-3 py-1 text-sm font-medium">
              New
            </span>
          )}
        </div>

        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Text
              propName="buttonText"
              placeholder="Add to Cart"
              renderBlock={(props) => (
                <button className="bg-white text-gray-900 px-6 py-3 font-medium hover:bg-gray-100 transition-colors duration-200">
                  {props.children}
                </button>
              )}
            />
          </div>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-6">
        {/* Product Name */}
        <Text
          propName="productName"
          placeholder="Beautiful Arrangement"
          renderBlock={(props) => (
            <h3 className="text-lg font-medium text-gray-900 mb-2 group-hover:text-green-700 transition-colors duration-200">
              {props.children}
            </h3>
          )}
        />

        {/* Price */}
        {showPrice && (
          <div className="flex items-center gap-2">
            {isOnSale && (
              <Text
                propName="originalPrice"
                placeholder="$45"
                renderBlock={(props) => (
                  <span className="text-gray-500 line-through text-sm">
                    {props.children}
                  </span>
                )}
              />
            )}
            <Text
              propName="currentPrice"
              placeholder="$35"
              renderBlock={(props) => (
                <span className={classNames(
                  "font-semibold",
                  isOnSale ? "text-red-600" : "text-gray-900"
                )}>
                  {props.children}
                </span>
              )}
            />
          </div>
        )}

        {/* Product Description */}
        <Text
          propName="description"
          placeholder="Fresh seasonal flowers arranged with care"
          renderBlock={(props) => (
            <p className="text-gray-600 text-sm mt-2 line-clamp-2">
              {props.children}
            </p>
          )}
        />
      </div>
    </div>
  )
}

ProductCard.schema = {
  name: 'product-card',
  label: 'Product Card',
  category: 'Florist',
  tags: ['product', 'card', 'florist', 'shop'],
  previewImageUrl: '/api/preview/product-card.png',
  
  getDefaultProps: () => ({
    isOnSale: false,
    isNew: false,
    showPrice: true,
    productName: 'Beautiful Arrangement',
    currentPrice: '$35',
    originalPrice: '$45',
    description: 'Fresh seasonal flowers arranged with care and attention to detail.',
    buttonText: 'Add to Cart',
  }),

  sideEditProps: [
    {
      name: 'isOnSale',
      label: 'On Sale',
      type: types.SideEditPropType.Boolean,
    },
    {
      name: 'isNew',
      label: 'New Product',
      type: types.SideEditPropType.Boolean,
    },
    {
      name: 'showPrice',
      label: 'Show Price',
      type: types.SideEditPropType.Boolean,
    },
  ],
}

export default ProductCard

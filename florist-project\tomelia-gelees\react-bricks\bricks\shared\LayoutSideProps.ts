import { types } from 'react-bricks/rsc'
import {
  highlightTextColors,
  bgColors,
  buttonColors,
  gradients,
} from './colors'
import { Padding, Size } from './components/Container'
import { Border } from './components/Section'

type RBUIColor = types.IColor & { className: string }

export interface LayoutProps {
  backgroundColor: RBUIColor
  backgroundImage: types.IImageSource
  borderTop: Border
  borderBottom: Border
  width: Size
  paddingTop: Padding
  paddingBottom: Padding
}

export const backgroundColorsEditProps: types.ISideEditProp<{
  backgroundColor: RBUIColor
}> = {
  name: 'backgroundColor',
  label: 'Background',
  type: types.SideEditPropType.Select,
  selectOptions: {
    display: types.OptionsDisplay.Color,
    options: [
      bgColors.WHITE,
      bgColors.LIGHT_GRAY,
      bgColors.GRAY,
      bgColors.ROSE,
      bgColors.ORANGE,
      bgColors.AMBER,
      bgColors.YELLOW,
      bgColors.LIME,
      bgColors.GREEN,
      bgColors.EMERALD,
      bgColors.TEAL,
      bgColors.CYAN,
      bgColors.SKY,
      bgColors.BLUE,
      bgColors.INDIGO,
      bgColors.VIOLET,
      bgColors.PURPLE,
      bgColors.FUCHSIA,
      bgColors.PINK,
    ],
  },
}

export const backgroundWithImageBgSideGroup: types.ISideGroup = {
  groupName: 'Background',
  defaultOpen: false,
  props: [
    backgroundColorsEditProps,
    {
      name: 'backgroundImage',
      label: 'Background image',
      type: types.SideEditPropType.Image,
    },
  ],
}

export const paddingBordersSideGroup: types.ISideGroup = {
  groupName: 'Spacing',
  defaultOpen: false,
  props: [
    {
      name: 'paddingTop',
      label: 'Padding top',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: '0', label: 'None' },
          { value: '2', label: 'Small' },
          { value: '4', label: 'Medium' },
          { value: '6', label: 'Large' },
          { value: '8', label: 'X-Large' },
          { value: '10', label: '2X-Large' },
          { value: '12', label: '3X-Large' },
          { value: '16', label: '4X-Large' },
          { value: '20', label: '5X-Large' },
        ],
      },
    },
    {
      name: 'paddingBottom',
      label: 'Padding bottom',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: '0', label: 'None' },
          { value: '2', label: 'Small' },
          { value: '4', label: 'Medium' },
          { value: '6', label: 'Large' },
          { value: '8', label: 'X-Large' },
          { value: '10', label: '2X-Large' },
          { value: '12', label: '3X-Large' },
          { value: '16', label: '4X-Large' },
          { value: '20', label: '5X-Large' },
        ],
      },
    },
  ],
}

export const textGradientEditProps: types.ISideEditProp<{
  textGradient: keyof typeof gradients
}> = {
  name: 'textGradient',
  label: 'Text gradient',
  type: types.SideEditPropType.Select,
  selectOptions: {
    display: types.OptionsDisplay.Select,
    options: [
      gradients.NONE,
      gradients.DAWN,
      gradients.OCEAN,
      gradients.FOREST,
    ],
  },
}

export const highlightTextEditProps: types.ISideEditProp<{
  highlightTextColor: { color: string; className: string }
}> = {
  name: 'highlightTextColor',
  label: 'Highlight color',
  type: types.SideEditPropType.Select,
  selectOptions: {
    display: types.OptionsDisplay.Color,
    options: [
      highlightTextColors.PINK,
      highlightTextColors.VIOLET,
      highlightTextColors.CYAN,
      highlightTextColors.LIME,
      highlightTextColors.SKY,
      highlightTextColors.ROSE,
      highlightTextColors.GREEN,
    ],
  },
}

export const sectionDefaults = {
  backgroundColor: bgColors.WHITE.value,
  borderTop: 'none' as Border,
  borderBottom: 'none' as Border,
  paddingTop: '12' as Padding,
  paddingBottom: '12' as Padding,
}

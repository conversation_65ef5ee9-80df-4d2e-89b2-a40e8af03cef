import { NavItem, Product, TeamMember, Service, ContactInfo, Feature } from '@/types';

export const navigationItems: NavItem[] = [
  {
    label: 'Home',
    href: '/',
    children: [
      { label: 'Orchid', href: '/' },
      { label: 'Daisy', href: '/daisy' },
      { label: 'Gardenia', href: '/gardenia' },
      { label: 'Dahlia', href: '/dahlia' },
      { label: 'Bluebell', href: '/bluebell' },
      { label: 'Primrose', href: '/primrose' },
      { label: 'Landing', href: '/landing' },
    ],
  },
  {
    label: 'Pages',
    href: '/pages',
    children: [
      { label: 'About Me', href: '/about-me' },
      { label: 'About Us', href: '/about-us' },
      { label: 'Contact Us', href: '/contact-us' },
      { label: 'Get In Touch', href: '/get-in-touch' },
      { label: 'Our Partners', href: '/our-partners' },
      { label: 'Our Services', href: '/our-services' },
      { label: 'Our Team', href: '/our-team' },
      { label: 'FAQ Page', href: '/faq-page' },
    ],
  },
  {
    label: 'Portfolio',
    href: '/portfolio',
    children: [
      { label: 'Standard', href: '/portfolio/standard' },
      { label: 'Gallery', href: '/portfolio/gallery' },
      { label: 'Gallery Joined', href: '/portfolio/gallery-joined' },
    ],
  },
  {
    label: 'Blog',
    href: '/blog',
    children: [
      { label: 'Right Sidebar', href: '/blog/right-sidebar' },
      { label: 'Left Sidebar', href: '/blog/left-sidebar' },
      { label: 'No Sidebar', href: '/blog/no-sidebar' },
      { label: 'Masonry Blog', href: '/blog/masonry' },
    ],
  },
  {
    label: 'Shop',
    href: '/shop',
    children: [
      { label: 'Shop List', href: '/shop' },
      { label: 'Cart', href: '/cart' },
      { label: 'Checkout', href: '/checkout' },
      { label: 'My Account', href: '/my-account' },
    ],
  },
];

export const contactInfo: ContactInfo = {
  address: '1087, Bathurst st Toronto ON',
  phone: '+364 1 65 6365',
  hours: 'Mon-Thu: 10:00-16:00 Sat-Sun: 10:00-16:00',
};

export const products: Product[] = [
  {
    id: '1',
    name: 'White',
    price: 25,
    originalPrice: 45,
    image: '/placeholder-flower-1.jpg',
    category: 'Anniversaries',
    isOnSale: true,
  },
  {
    id: '2',
    name: 'Impatiens',
    price: 45,
    image: '/placeholder-flower-2.jpg',
    category: 'Birthdays',
    isNew: true,
  },
  {
    id: '3',
    name: 'Mazus',
    price: 45,
    image: '/placeholder-flower-3.jpg',
    category: 'Gifts',
  },
  {
    id: '4',
    name: 'Pansies',
    price: 45,
    image: '/placeholder-flower-4.jpg',
    category: 'Anniversaries',
  },
  {
    id: '5',
    name: 'Dahlia',
    price: 45,
    image: '/placeholder-flower-5.jpg',
    category: 'Birthdays',
  },
  {
    id: '6',
    name: 'Peony',
    price: 45,
    image: '/placeholder-flower-6.jpg',
    category: 'Gifts',
  },
];

export const teamMembers: TeamMember[] = [
  {
    id: '1',
    name: 'Velva Kopf',
    role: 'Biologist',
    image: '/placeholder-team-1.jpg',
  },
  {
    id: '2',
    name: 'Elizabeth Morris',
    role: 'Florist',
    image: '/placeholder-team-2.jpg',
  },
  {
    id: '3',
    name: 'Blaine Bush',
    role: 'Photographer',
    image: '/placeholder-team-3.jpg',
  },
];

export const services: Service[] = [
  {
    id: '1',
    title: 'New arrangements',
    description: 'Fresh and creative floral arrangements',
    icon: '/placeholder-icon-1.png',
  },
  {
    id: '2',
    title: 'Flower specialist',
    description: 'Expert knowledge in flower care',
    icon: '/placeholder-icon-2.png',
  },
];

export const features: Feature[] = [
  {
    id: '1',
    title: 'Online Order',
    description: 'Easy online ordering system',
    icon: '/placeholder-feature-1.png',
    link: '#',
  },
  {
    id: '2',
    title: 'Delivery in 2-4 h',
    description: 'Fast delivery service',
    icon: '/placeholder-feature-2.png',
    link: '#',
  },
  {
    id: '3',
    title: 'Freshness',
    description: 'Always fresh flowers',
    icon: '/placeholder-feature-3.png',
    link: '#',
  },
  {
    id: '4',
    title: 'Made by Artists',
    description: 'Crafted by professional florists',
    icon: '/placeholder-feature-4.png',
    link: '#',
  },
];

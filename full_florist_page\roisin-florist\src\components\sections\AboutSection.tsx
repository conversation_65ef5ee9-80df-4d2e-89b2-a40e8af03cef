import React from 'react';
import Image from '@/components/ui/Image';
import Container from '@/components/ui/Container';

const AboutSection: React.FC = () => {
  return (
    <section className="py-20 bg-gray-50">
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                We Produce Beauty
                <br />
                <span className="text-green-700">Inspired by Flora</span>
              </h2>
            </div>
            
            <p className="text-lg text-gray-600 leading-relaxed">
              Lorem ipsum dolor sit amet, pri autem nemore bonorum te. Autem fierent 
              ullamcorper ius no, nec ea quodsi invenire. Pri facilisi eleifend ad, 
              ad eos scripta oblique. Vix cu oratio.
            </p>

            {/* Owner Quote */}
            <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-green-600">
              <p className="text-gray-700 italic mb-3">
                "We believe that every flower tells a story, and every arrangement 
                captures a moment of pure beauty."
              </p>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-700 font-semibold text-sm">CM</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Camila Menson</p>
                  <p className="text-sm text-gray-600">Owner of Roisin Store</p>
                </div>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="relative">
            <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-lg">
              <Image
                src="/about-image.jpg"
                alt="Florist arranging beautiful flowers"
                fill
                className="object-cover"
                unsplashQuery="florist,woman,flowers,arranging"
                unsplashSize="600x800"
              />
            </div>
            
            {/* Floating Card */}
            <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-700 mb-1">15+</div>
                <div className="text-sm text-gray-600">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default AboutSection;

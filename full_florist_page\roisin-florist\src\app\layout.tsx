import type { Metada<PERSON> } from "next";
import { Inter, Crimson_Text } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const crimsonText = Crimson_Text({
  variable: "--font-crimson",
  subsets: ["latin"],
  weight: ["400", "600", "700"],
  style: ["normal", "italic"],
});

export const metadata: Metadata = {
  title: "Roisin - Beautiful Florist & Plant Lover",
  description: "We produce beauty inspired by flora. Professional florist services in Ottawa with fresh flowers, arrangements, and plant care.",
  keywords: "florist, flowers, bouquet, plants, Ottawa, wedding flowers, arrangements",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${crimsonText.variable} font-sans antialiased`}
      >
        <Header />
        {children}
        <Footer />
      </body>
    </html>
  );
}

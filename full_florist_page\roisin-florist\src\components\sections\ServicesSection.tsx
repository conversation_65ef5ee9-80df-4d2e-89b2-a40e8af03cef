import React from 'react';
import Image from '@/components/ui/Image';
import Container from '@/components/ui/Container';
import { services } from '@/data/mockData';

const ServicesSection: React.FC = () => {
  return (
    <section className="py-20 bg-white">
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Side - Images */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="relative h-48 rounded-lg overflow-hidden">
                <Image
                  src="/service-1.jpg"
                  alt="New arrangements"
                  fill
                  className="object-cover"
                  unsplashQuery="flower,arrangement,bouquet"
                  unsplashSize="400x300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                <div className="absolute bottom-4 left-4 text-white">
                  <h3 className="font-semibold">New arrangements</h3>
                </div>
              </div>
              
              <div className="relative h-32 rounded-lg overflow-hidden">
                <Image
                  src="/service-2.jpg"
                  alt="Flower specialist"
                  fill
                  className="object-cover"
                  unsplashQuery="florist,specialist,flowers"
                  unsplashSize="400x200"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                <div className="absolute bottom-3 left-3 text-white">
                  <h3 className="font-semibold text-sm">Flower specialist</h3>
                </div>
              </div>
            </div>
            
            <div className="relative h-80 rounded-lg overflow-hidden">
              <Image
                src="/service-main.jpg"
                alt="Ottawa Florist"
                fill
                className="object-cover"
                unsplashQuery="florist,shop,flowers,ottawa"
                unsplashSize="400x500"
              />
            </div>
          </div>

          {/* Right Side - Content */}
          <div className="space-y-6">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight mb-4">
                Ottawa Florist
                <br />
                <span className="text-green-700">& Plant Lover</span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                Lorem ipsum dolor sit amet summ dolore, eu omnes mnesarchum eosatsimuscum primis.
              </p>
            </div>

            {/* Service Cards */}
            <div className="space-y-4">
              <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-green-700 font-bold">01</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Suzan Mary</h3>
                  <p className="text-sm text-gray-600">No. 01 trees specialist</p>
                </div>
              </div>

              <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-green-700 font-bold">02</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Malva Dali</h3>
                  <p className="text-sm text-gray-600">No. 02 trees specialist</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section - Special Offers */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="relative h-64 rounded-lg overflow-hidden group cursor-pointer">
            <Image
              src="/offer-1.jpg"
              alt="Perfect gifts"
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              unsplashQuery="gift,flowers,bouquet"
              unsplashSize="400x400"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
            <div className="absolute bottom-6 left-6 text-white">
              <h3 className="text-xl font-semibold mb-1">Perfect gifts</h3>
              <p className="text-green-300 font-medium">On sale</p>
              <p className="text-sm opacity-90">Fraser, specializes</p>
            </div>
          </div>

          <div className="relative h-64 rounded-lg overflow-hidden group cursor-pointer">
            <Image
              src="/offer-2.jpg"
              alt="House plants"
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              unsplashQuery="house,plants,indoor"
              unsplashSize="400x400"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
            <div className="absolute bottom-6 left-6 text-white">
              <h3 className="text-xl font-semibold mb-1">House plants</h3>
              <p className="text-green-300 font-medium">20% discount</p>
            </div>
          </div>

          <div className="relative h-64 rounded-lg overflow-hidden group cursor-pointer">
            <Image
              src="/offer-3.jpg"
              alt="Flower decoration"
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              unsplashQuery="flower,decoration,wedding"
              unsplashSize="400x400"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
            <div className="absolute bottom-6 left-6 text-white">
              <h3 className="text-xl font-semibold mb-1">Flower decoration</h3>
              <p className="text-green-300 font-medium">Free delivery</p>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default ServicesSection;

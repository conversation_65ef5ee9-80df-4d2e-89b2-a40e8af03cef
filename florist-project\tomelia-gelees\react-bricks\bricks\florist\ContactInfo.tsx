import React from 'react'
import { Text, RichText, types } from 'react-bricks/rsc'
import classNames from 'classnames'

interface ContactInfoProps {
  backgroundColor: types.IColor
  layout: 'horizontal' | 'vertical'
  showMap: boolean
}

const ContactInfo: types.Brick<ContactInfoProps> = ({
  backgroundColor = { color: '#f8f9fa', className: 'bg-gray-50' },
  layout = 'horizontal',
  showMap = true,
}) => {
  return (
    <section
      className={classNames('py-16', backgroundColor?.className)}
      style={{ backgroundColor: backgroundColor?.color }}
    >
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <Text
            propName="sectionTitle"
            placeholder="Visit Our Flower Shop"
            renderBlock={(props) => (
              <h2 className="text-4xl md:text-5xl font-light text-gray-900 mb-6">
                {props.children}
              </h2>
            )}
          />
          
          <RichText
            propName="sectionDescription"
            placeholder="Come visit us in person to see our beautiful flowers and speak with our expert florists."
            renderBlock={(props) => (
              <div className="text-lg text-gray-600 max-w-2xl mx-auto">
                {props.children}
              </div>
            )}
            allowedFeatures={[
              types.RichTextFeatures.Bold,
              types.RichTextFeatures.Italic,
            ]}
          />
        </div>

        {/* Contact Content */}
        <div className={classNames(
          'grid gap-12',
          layout === 'horizontal' ? 'lg:grid-cols-2' : 'grid-cols-1 max-w-2xl mx-auto'
        )}>
          {/* Contact Details */}
          <div className="space-y-8">
            {/* Address */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Address</h3>
                <Text
                  propName="address"
                  placeholder="1087, Bathurst st Toronto ON"
                  renderBlock={(props) => (
                    <p className="text-gray-600">
                      {props.children}
                    </p>
                  )}
                />
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Phone</h3>
                <Text
                  propName="phone"
                  placeholder="5/ 63 47"
                  renderBlock={(props) => (
                    <p className="text-gray-600">
                      {props.children}
                    </p>
                  )}
                />
              </div>
            </div>

            {/* Email */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Email</h3>
                <Text
                  propName="email"
                  placeholder="<EMAIL>"
                  renderBlock={(props) => (
                    <p className="text-gray-600">
                      {props.children}
                    </p>
                  )}
                />
              </div>
            </div>

            {/* Hours */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Hours</h3>
                <div className="space-y-1 text-gray-600">
                  <Text
                    propName="weekdayHours"
                    placeholder="Mon-Thu: 10:00-16:00"
                    renderBlock={(props) => (
                      <p>{props.children}</p>
                    )}
                  />
                  <Text
                    propName="weekendHours"
                    placeholder="Sat-Sun: 10:00-16:00"
                    renderBlock={(props) => (
                      <p>{props.children}</p>
                    )}
                  />
                  <Text
                    propName="fridayHours"
                    placeholder="Friday: Closed"
                    renderBlock={(props) => (
                      <p>{props.children}</p>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Map or Additional Info */}
          {showMap && (
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="aspect-video bg-gray-200 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <p className="text-sm">Interactive Map</p>
                  <p className="text-xs text-gray-400">Click to view directions</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Text
            propName="ctaText"
            placeholder="Get Directions"
            renderBlock={(props) => (
              <a
                href="#"
                className="inline-block bg-green-600 text-white px-8 py-3 font-medium hover:bg-green-700 transition-colors duration-200"
              >
                {props.children}
              </a>
            )}
          />
        </div>
      </div>
    </section>
  )
}

ContactInfo.schema = {
  name: 'contact-info',
  label: 'Contact Information',
  category: 'Florist',
  tags: ['contact', 'location', 'address', 'hours'],
  previewImageUrl: '/api/preview/contact-info.png',
  
  getDefaultProps: () => ({
    backgroundColor: { color: '#f8f9fa', className: 'bg-gray-50' },
    layout: 'horizontal',
    showMap: true,
    sectionTitle: 'Visit Our Flower Shop',
    sectionDescription: 'Come visit us in person to see our beautiful flowers and speak with our expert florists.',
    address: '1087, Bathurst st Toronto ON',
    phone: '5/ 63 47',
    email: '<EMAIL>',
    weekdayHours: 'Mon-Thu: 10:00-16:00',
    weekendHours: 'Sat-Sun: 10:00-16:00',
    fridayHours: 'Friday: Closed',
    ctaText: 'Get Directions',
  }),

  sideEditProps: [
    {
      name: 'backgroundColor',
      label: 'Background Color',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Color,
        options: [
          { value: { color: '#ffffff', className: 'bg-white' }, label: 'White' },
          { value: { color: '#f8f9fa', className: 'bg-gray-50' }, label: 'Light Gray' },
          { value: { color: '#f0f8f0', className: 'bg-green-25' }, label: 'Light Green' },
        ],
      },
    },
    {
      name: 'layout',
      label: 'Layout Style',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 'horizontal', label: 'Horizontal (2 columns)' },
          { value: 'vertical', label: 'Vertical (1 column)' },
        ],
      },
    },
    {
      name: 'showMap',
      label: 'Show Map Placeholder',
      type: types.SideEditPropType.Boolean,
    },
  ],
}

export default ContactInfo
